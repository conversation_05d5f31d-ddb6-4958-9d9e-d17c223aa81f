# MignalyBot

MignalyBot is an AI-powered Telegram bot that acts as a content creator and professional trader/analyst. It collects market data, news, and economic events, then uses Qwen 2.5 AI to generate insightful posts for Telegram channels.

## Features

- **Data Collection**: Automatically collects candle data, financial news, and economic calendar events
- **AI-Powered Content**: Uses Qwen 2.5 Max 2025 to analyze data and generate engaging posts
- **Trading Signals**: Implements customizable trading strategies and tracks their performance
- **Multi-Channel Support**: Manages multiple Telegram channels with different configurations
- **Scheduling System**: Automatically schedules and posts content at optimal times
- **Admin Dashboard**: Web-based admin interface for configuration and management

## Setup Instructions

### Prerequisites

- Python 3.8 or higher
- Telegram Bo<PERSON>ken (from BotFather)
- Qwen API Key

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/mignalybot.git
   cd mignalybot
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file from the example:
   ```
   cp .env.example .env
   ```

5. Edit the `.env` file with your configuration:
   ```
   # Database configuration
   DATABASE_URL=sqlite:///mignalybot.db

   # Telegram Bot configuration
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

   # API Keys
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
   QWEN_API_KEY=your_qwen_api_key_here

   # Admin configuration
   ADMIN_USERNAME=admin
   ADMIN_PASSWORD=change_this_password

   # Application settings
   DEBUG=True
   LOG_LEVEL=INFO
   TIMEZONE=UTC
   ```

### Running the Application

1. Initialize the project:
   ```
   python setup.py
   ```

2. Start the full application:
   ```
   python main.py
   ```

3. Or run individual components:
   ```
   python run_admin.py         # Run only the admin dashboard
   python run_telegram_bot.py  # Run only the Telegram bot
   python run_data_collection.py  # Run only the data collection
   python run_content_generation.py  # Run only the content generation
   ```

4. Access the admin dashboard:
   Open your browser and navigate to `http://localhost:8000`

   Login with the credentials you set in the `.env` file.

### Testing Components

1. Test the Qwen API integration:
   ```
   python test_qwen.py [api_key]
   ```

2. Test the Telegram bot integration:
   ```
   python test_telegram.py [bot_token] [chat_id]
   ```

3. Create a sample trading signal:
   ```
   python create_sample_signal.py
   ```

4. Create a sample post:
   ```
   python create_sample_post.py
   ```

5. Test chart generation:
   ```
   python test_chart.py [signal_id]
   ```

## Usage Guide

### Admin Dashboard

The admin dashboard provides a user-friendly interface to manage all aspects of MignalyBot:

1. **Dashboard**: Overview of system status and statistics
2. **Configuration**: Global settings including API keys and data collection frequency
3. **Channels**: Manage Telegram channels where content will be posted
4. **Strategies**: Create and edit trading strategies
5. **Posts**: View, create, and manage posts
6. **Signals**: Monitor trading signals and their performance
7. **News**: Browse collected news items
8. **Events**: View upcoming economic events

### Adding a Telegram Channel

1. Create a Telegram channel
2. Add your bot as an administrator to the channel
3. Get the channel's chat ID (you can use @username_to_id_bot)
4. In the admin dashboard, go to "Channels" and click "Add Channel"
5. Enter the channel details including the chat ID
6. Select which post types should be sent to this channel

### Creating a Trading Strategy

1. In the admin dashboard, go to "Strategies" and click "Add Strategy"
2. Enter a name and description for the strategy
3. Specify which symbols and timeframes the strategy should analyze
4. Define the strategy parameters
5. Write the strategy code using the provided template
6. Save the strategy

### Generating Content

Content generation happens automatically based on the configured frequency. You can also trigger it manually:

1. In the admin dashboard, click the "Generate Content" button
2. The system will collect data, analyze it, and create posts
3. Posts will be scheduled and published automatically

## Default Trading Strategies

MignalyBot comes with several pre-configured trading strategies:

1. **EMA Crossover Strategy**: Generates signals based on fast and slow EMA crossovers
2. **RSI Divergence Strategy**: Identifies potential trend reversals using RSI divergence
3. **Support/Resistance Breakout Strategy**: Detects breakouts from key support and resistance levels

You can customize these strategies or create your own in the admin dashboard.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [python-telegram-bot](https://github.com/python-telegram-bot/python-telegram-bot) for Telegram integration
- [FastAPI](https://fastapi.tiangolo.com/) for the admin dashboard
- [SQLAlchemy](https://www.sqlalchemy.org/) for database operations
- [yfinance](https://github.com/ranaroussi/yfinance) for market data
- [pandas-ta](https://github.com/twopirllc/pandas-ta) for technical analysis indicators
