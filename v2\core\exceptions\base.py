"""
Custom exception classes for MignalyBot v2

Provides structured error handling with:
- Error categorization
- Error context
- Recovery suggestions
- Performance impact tracking
"""

from typing import Optional, Dict, Any
from enum import Enum


class ErrorCategory(Enum):
    """Error categories for better error handling"""
    DATABASE = "database"
    AI_API = "ai_api"
    TELEGRAM = "telegram"
    DATA_COLLECTION = "data_collection"
    CONFIGURATION = "configuration"
    VALIDATION = "validation"
    NETWORK = "network"
    PERFORMANCE = "performance"
    UNKNOWN = "unknown"


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class MignalyBotException(Exception):
    """Base exception class for MignalyBot v2"""
    
    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        recovery_suggestion: Optional[str] = None,
        original_exception: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.recovery_suggestion = recovery_suggestion
        self.original_exception = original_exception
    
    def __str__(self) -> str:
        return f"[{self.category.value.upper()}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging/serialization"""
        return {
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "context": self.context,
            "recovery_suggestion": self.recovery_suggestion,
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class DatabaseException(MignalyBotException):
    """Database-related exceptions"""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        table: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if operation:
            context['operation'] = operation
        if table:
            context['table'] = table
        
        # Remove context from kwargs to avoid duplicate
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'context'}

        super().__init__(
            message,
            category=ErrorCategory.DATABASE,
            context=context,
            **filtered_kwargs
        )


class AIAPIException(MignalyBotException):
    """AI API-related exceptions"""
    
    def __init__(
        self,
        message: str,
        api_endpoint: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if api_endpoint:
            context['api_endpoint'] = api_endpoint
        if status_code:
            context['status_code'] = status_code
        
        # Remove context from kwargs to avoid duplicate
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'context'}

        super().__init__(
            message,
            category=ErrorCategory.AI_API,
            context=context,
            **filtered_kwargs
        )


class TelegramException(MignalyBotException):
    """Telegram-related exceptions"""
    
    def __init__(
        self,
        message: str,
        chat_id: Optional[str] = None,
        message_id: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if chat_id:
            context['chat_id'] = chat_id
        if message_id:
            context['message_id'] = message_id
        
        super().__init__(
            message,
            category=ErrorCategory.TELEGRAM,
            context=context,
            **kwargs
        )


class DataCollectionException(MignalyBotException):
    """Data collection-related exceptions"""
    
    def __init__(
        self,
        message: str,
        source: Optional[str] = None,
        symbol: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if source:
            context['source'] = source
        if symbol:
            context['symbol'] = symbol
        
        super().__init__(
            message,
            category=ErrorCategory.DATA_COLLECTION,
            context=context,
            **kwargs
        )


class ConfigurationException(MignalyBotException):
    """Configuration-related exceptions"""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if config_key:
            context['config_key'] = config_key
        
        super().__init__(
            message,
            category=ErrorCategory.CONFIGURATION,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )


class ValidationException(MignalyBotException):
    """Validation-related exceptions"""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if field:
            context['field'] = field
        if value is not None:
            context['value'] = str(value)
        
        super().__init__(
            message,
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            context=context,
            **kwargs
        )


class NetworkException(MignalyBotException):
    """Network-related exceptions"""
    
    def __init__(
        self,
        message: str,
        url: Optional[str] = None,
        status_code: Optional[int] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if url:
            context['url'] = url
        if status_code:
            context['status_code'] = status_code
        
        super().__init__(
            message,
            category=ErrorCategory.NETWORK,
            context=context,
            **kwargs
        )


class PerformanceException(MignalyBotException):
    """Performance-related exceptions"""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        duration: Optional[float] = None,
        memory_usage: Optional[float] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if operation:
            context['operation'] = operation
        if duration:
            context['duration_ms'] = duration * 1000
        if memory_usage:
            context['memory_mb'] = memory_usage
        
        super().__init__(
            message,
            category=ErrorCategory.PERFORMANCE,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs
        )


def handle_exception(
    exception: Exception,
    logger,
    operation: str = "unknown",
    reraise: bool = True
) -> Optional[MignalyBotException]:
    """
    Handle and convert exceptions to MignalyBotException
    
    Args:
        exception: Original exception
        logger: Logger instance
        operation: Operation being performed
        reraise: Whether to reraise the exception
        
    Returns:
        MignalyBotException if not reraised
    """
    if isinstance(exception, MignalyBotException):
        mignalybot_exception = exception
    else:
        # Convert to MignalyBotException
        mignalybot_exception = MignalyBotException(
            message=str(exception),
            context={'operation': operation},
            original_exception=exception
        )
    
    # Log the exception
    logger.error(
        f"Exception in {operation}: {mignalybot_exception.message}",
        extra={
            'exception_category': mignalybot_exception.category.value,
            'exception_severity': mignalybot_exception.severity.value,
            'exception_context': mignalybot_exception.context
        },
        exc_info=True
    )
    
    if reraise:
        raise mignalybot_exception
    
    return mignalybot_exception
