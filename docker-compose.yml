services:
  mignalybot:
    build: .
    container_name: mignalybot
    ports:
      - "9000:9000"
    volumes:
      - ./.env:/app/.env:ro
      - ./data:/app/data
      - ./logs:/app/logs
      - ./images:/app/images
      - /etc/localtime:/etc/localtime:ro
      - /usr/share/zoneinfo/Asia/Tehran:/etc/timezone:ro
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - PORT=9000
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - TZ=Asia/Tehran
      - TIMEZONE=Asia/Tehran
      - DATABASE_URL=sqlite:///data/mignalybot.db
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
      - QWEN_API_KEY=${QWEN_API_KEY:-}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-}
