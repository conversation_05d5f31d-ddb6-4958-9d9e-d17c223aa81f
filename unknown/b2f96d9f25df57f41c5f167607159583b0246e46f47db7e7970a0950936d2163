{% extends "base.html" %}

{% block title %}Channels - MignalyBot Admin{% endblock %}

{% block page_title %}Channels{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Telegram Channels</h5>
        <button type="button" class="btn btn-primary" id="addChannelBtn">
            <i class="fas fa-plus"></i> Add Channel
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="channelsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Chat ID</th>
                        <th>Language</th>
                        <th>Post Types</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">Loading channels...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Channel Modal -->
<div class="modal fade" id="channelModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="channelModalTitle">Add Channel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="channelForm">
                    <input type="hidden" id="channelId">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channelName" class="form-label">Channel Name</label>
                                <input type="text" class="form-control" id="channelName" name="name" required>
                                <div class="form-text">Display name for this channel</div>
                            </div>

                            <div class="mb-3">
                                <label for="channelChatId" class="form-label">Chat ID</label>
                                <input type="text" class="form-control" id="channelChatId" name="chat_id" required>
                                <div class="form-text">Telegram chat ID for the channel</div>
                            </div>

                            <div class="mb-3">
                                <label for="channelLanguage" class="form-label">Language</label>
                                <select class="form-select" id="channelLanguage" name="language" required>
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                    <option value="it">Italian</option>
                                    <option value="pt">Portuguese</option>
                                    <option value="ru">Russian</option>
                                    <option value="zh">Chinese</option>
                                    <option value="ja">Japanese</option>
                                    <option value="ko">Korean</option>
                                    <option value="ar">Arabic</option>
                                    <option value="fa">Farsi (Persian)</option>
                                    <option value="hi">Hindi</option>
                                    <option value="tr">Turkish</option>
                                </select>
                                <div class="form-text">Language for content generation</div>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="channelActive" name="active" checked>
                                <label class="form-check-label" for="channelActive">Active</label>
                                <div class="form-text">Enable or disable this channel</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channelBrandName" class="form-label">Brand Name</label>
                                <input type="text" class="form-control" id="channelBrandName" name="brand_name">
                                <div class="form-text">Brand name for content personalization</div>
                            </div>

                            <div class="mb-3">
                                <label for="channelUsername" class="form-label">Channel Username</label>
                                <input type="text" class="form-control" id="channelUsername" name="channel_username" placeholder="mignalyfx">
                                <div class="form-text">Channel username for @mentions (without @)</div>
                            </div>

                            <div class="mb-3">
                                <label for="channelDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="channelDescription" name="description" rows="2"></textarea>
                                <div class="form-text">Channel description</div>
                            </div>

                            <div class="mb-3">
                                <label for="channelBrandDescription" class="form-label">Brand Description</label>
                                <textarea class="form-control" id="channelBrandDescription" name="brand_description" rows="2"></textarea>
                                <div class="form-text">Brand description for content personalization</div>
                            </div>
                        </div>
                    </div>

                    <!-- Advertisement Settings -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6 class="mb-3">Advertisement Settings</h6>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="channelEnableAdvertisement" name="enable_advertisement">
                                    <label class="form-check-label" for="channelEnableAdvertisement">
                                        Enable Advertisement Footer
                                    </label>
                                    <div class="form-text">Add advertisement footer to all messages sent to this channel</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="channelAdvertisementText" class="form-label">Advertisement Text</label>
                                <input type="text" class="form-control" id="channelAdvertisementText" name="advertisement_text" value="This message generated by Mignaly">
                                <div class="form-text">Text to display in the advertisement footer</div>
                            </div>

                            <div class="mb-3">
                                <label for="channelAdvertisementUrl" class="form-label">Advertisement URL</label>
                                <input type="url" class="form-control" id="channelAdvertisementUrl" name="advertisement_url" value="https://mignaly.com">
                                <div class="form-text">URL to link to in the advertisement footer</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Post Types</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input post-type-check" type="checkbox" id="postTypeNews" value="news" checked>
                                    <label class="form-check-label" for="postTypeNews">📰 News</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input post-type-check" type="checkbox" id="postTypeSignal" value="signal" checked>
                                    <label class="form-check-label" for="postTypeSignal">📊 Signals</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input post-type-check" type="checkbox" id="postTypeAnalysis" value="analysis" checked>
                                    <label class="form-check-label" for="postTypeAnalysis">📈 Analysis</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input post-type-check" type="checkbox" id="postTypeEvent" value="event" checked>
                                    <label class="form-check-label" for="postTypeEvent">📅 Events</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input post-type-check" type="checkbox" id="postTypePerformance" value="performance" checked>
                                    <label class="form-check-label" for="postTypePerformance">📊 Performance</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input post-type-check" type="checkbox" id="postTypeGreeting" value="greeting" checked>
                                    <label class="form-check-label" for="postTypeGreeting">👋 Greeting</label>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="channelPostTypes" name="post_types">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveChannelBtn">Save Channel</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteChannelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this channel? This action cannot be undone.</p>
                <p><strong>Channel: </strong><span id="deleteChannelName"></span></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Load channels
        loadChannels();

        // Add channel button
        $('#addChannelBtn').click(function() {
            resetChannelForm();
            $('#channelModalTitle').text('Add Channel');
            $('#channelModal').modal('show');
        });

        // Save channel button
        $('#saveChannelBtn').click(function() {
            saveChannel();
        });

        // Update post types hidden field when checkboxes change
        $('.post-type-check').change(function() {
            updatePostTypesField();
        });
    });

    function loadChannels() {
        $.ajax({
            url: '/api/channels',
            type: 'GET',
            success: function(data) {
                let tableHtml = '';

                if (data.length === 0) {
                    tableHtml = '<tr><td colspan="7" class="text-center">No channels found</td></tr>';
                } else {
                    data.forEach(function(channel, index) {
                        const postTypes = channel.post_types.split(',').map(type =>
                            `<span class="badge bg-info me-1">${type}</span>`
                        ).join('');

                        tableHtml += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${channel.name}</td>
                                <td>${channel.chat_id}</td>
                                <td>${channel.language}</td>
                                <td>${postTypes}</td>
                                <td>
                                    <span class="badge ${channel.active ? 'bg-success' : 'bg-danger'}">
                                        ${channel.active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-channel-btn" data-channel='${JSON.stringify(channel)}'>
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-channel-btn" data-channel-id="${channel.id}" data-channel-name="${channel.name}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                }

                $('#channelsTable tbody').html(tableHtml);

                // Set up edit button handlers
                $('.edit-channel-btn').click(function() {
                    const channel = $(this).data('channel');
                    editChannel(channel);
                });

                // Set up delete button handlers
                $('.delete-channel-btn').click(function() {
                    const channelId = $(this).data('channel-id');
                    const channelName = $(this).data('channel-name');
                    showDeleteConfirmation(channelId, channelName);
                });
            },
            error: function(xhr) {
                alert('Error loading channels: ' + xhr.responseJSON.detail);
            }
        });
    }

    function resetChannelForm() {
        $('#channelId').val('');
        $('#channelForm')[0].reset();

        // Check all post type checkboxes
        $('.post-type-check').prop('checked', true);
        updatePostTypesField();
    }

    function editChannel(channel) {
        resetChannelForm();

        $('#channelId').val(channel.id);
        $('#channelName').val(channel.name);
        $('#channelChatId').val(channel.chat_id);
        $('#channelLanguage').val(channel.language);
        $('#channelActive').prop('checked', channel.active);
        $('#channelBrandName').val(channel.brand_name);
        $('#channelUsername').val(channel.channel_username);
        $('#channelDescription').val(channel.description);
        $('#channelBrandDescription').val(channel.brand_description);
        $('#channelEnableAdvertisement').prop('checked', channel.enable_advertisement || false);
        $('#channelAdvertisementText').val(channel.advertisement_text || 'This message generated by Mignaly');
        $('#channelAdvertisementUrl').val(channel.advertisement_url || 'https://mignaly.com');

        // Set post type checkboxes
        const postTypes = channel.post_types.split(',');
        $('.post-type-check').each(function() {
            $(this).prop('checked', postTypes.includes($(this).val()));
        });
        updatePostTypesField();

        $('#channelModalTitle').text('Edit Channel');
        $('#channelModal').modal('show');
    }

    function updatePostTypesField() {
        const selectedTypes = [];
        $('.post-type-check:checked').each(function() {
            selectedTypes.push($(this).val());
        });
        $('#channelPostTypes').val(selectedTypes.join(','));
    }

    function saveChannel() {
        // Update post types field
        updatePostTypesField();

        const channelId = $('#channelId').val();
        const formData = {
            name: $('#channelName').val(),
            chat_id: $('#channelChatId').val(),
            language: $('#channelLanguage').val(),
            active: $('#channelActive').is(':checked'),
            brand_name: $('#channelBrandName').val(),
            channel_username: $('#channelUsername').val(),
            description: $('#channelDescription').val(),
            brand_description: $('#channelBrandDescription').val(),
            enable_advertisement: $('#channelEnableAdvertisement').is(':checked'),
            advertisement_text: $('#channelAdvertisementText').val(),
            advertisement_url: $('#channelAdvertisementUrl').val(),
            post_types: $('#channelPostTypes').val()
        };

        const url = channelId ? `/api/channels/${channelId}` : '/api/channels';
        const method = channelId ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            type: method,
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#channelModal').modal('hide');
                loadChannels();
                alert(`Channel ${channelId ? 'updated' : 'created'} successfully!`);
            },
            error: function(xhr) {
                alert(`Error ${channelId ? 'updating' : 'creating'} channel: ` + xhr.responseJSON.detail);
            }
        });
    }

    function showDeleteConfirmation(channelId, channelName) {
        $('#deleteChannelName').text(channelName);
        $('#confirmDeleteBtn').data('channel-id', channelId);
        $('#deleteChannelModal').modal('show');

        // Set up delete confirmation button
        $('#confirmDeleteBtn').off('click').click(function() {
            const id = $(this).data('channel-id');
            deleteChannel(id);
        });
    }

    function deleteChannel(channelId) {
        $.ajax({
            url: `/api/channels/${channelId}`,
            type: 'DELETE',
            success: function(response) {
                $('#deleteChannelModal').modal('hide');
                loadChannels();
                alert('Channel deleted successfully!');
            },
            error: function(xhr) {
                alert('Error deleting channel: ' + xhr.responseJSON.detail);
            }
        });
    }
</script>
{% endblock %}
