"""
Utility functions for cleaning duplicate posts
"""

import logging
from datetime import datetime, timezone
from sqlalchemy import select, func, and_, delete, text
from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Post, PostType

logger = logging.getLogger(__name__)


async def delete_duplicate_performance_posts():
    """
    Delete duplicate performance posts for the same signal and channel.
    Keeps the first occurrence (oldest created_at) and deletes the rest.
    """
    logger.info("Starting duplicate performance post cleanup...")
    
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                # For SQLite, get all performance posts and manually find duplicates
                all_posts_result = db.execute(
                    select(Post).where(
                        Post.type == PostType.PERFORMANCE,
                        Post.signal_id.is_not(None)
                    ).order_by(Post.created_at)
                )
                all_posts = all_posts_result.scalars().all()
                
                # Track seen combinations and duplicates to delete
                seen_combinations = set()
                duplicates_to_delete = []
                
                for post in all_posts:
                    # Create a unique key based on channel_id, signal_id, and type
                    post_key = (post.channel_id, post.signal_id, post.type.value)
                    
                    if post_key in seen_combinations:
                        duplicates_to_delete.append(post.id)
                        logger.info(f"Marking duplicate performance post {post.id} for deletion (channel: {post.channel_id}, signal: {post.signal_id})")
                    else:
                        seen_combinations.add(post_key)
                
                # Delete duplicates
                deleted_count = 0
                for post_id in duplicates_to_delete:
                    db.execute(delete(Post).where(Post.id == post_id))
                    deleted_count += 1
                
                if deleted_count > 0:
                    db.commit()
                    logger.info(f"Deleted {deleted_count} duplicate performance posts from SQLite database")
                else:
                    logger.info("No duplicate performance posts found in SQLite database")
                    
            else:
                # For PostgreSQL, use a more efficient approach with window functions
                duplicate_ids_query = """
                WITH duplicates AS (
                    SELECT id,
                           ROW_NUMBER() OVER (
                               PARTITION BY channel_id, signal_id, type
                               ORDER BY created_at
                           ) as row_num
                    FROM posts
                    WHERE type = 'performance' AND signal_id IS NOT NULL
                )
                SELECT id FROM duplicates WHERE row_num > 1
                """

                duplicate_ids_result = await db.execute(text(duplicate_ids_query))
                duplicate_ids = [row[0] for row in duplicate_ids_result.fetchall()]

                deleted_count = 0
                if duplicate_ids:
                    logger.info(f"Found {len(duplicate_ids)} duplicate performance posts to delete")
                    delete_query = delete(Post).where(Post.id.in_(duplicate_ids))
                    result = await db.execute(delete_query)
                    deleted_count = result.rowcount

                    if deleted_count > 0:
                        await db.commit()
                        logger.info(f"Deleted {deleted_count} duplicate performance posts from PostgreSQL database")
                    else:
                        logger.info("No duplicate performance posts found in PostgreSQL database")
                else:
                    logger.info("No duplicate performance posts found in PostgreSQL database")
            
            return deleted_count if 'deleted_count' in locals() else 0
            
        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error deleting duplicate performance posts: {e}", exc_info=True)
            raise


async def delete_duplicate_signal_posts():
    """
    Delete duplicate signal posts for the same signal and channel.
    Keeps the first occurrence (oldest created_at) and deletes the rest.
    """
    logger.info("Starting duplicate signal post cleanup...")
    
    async for db in get_async_db():
        try:
            if is_sqlite_db():
                # For SQLite, get all signal posts and manually find duplicates
                all_posts_result = db.execute(
                    select(Post).where(
                        Post.type == PostType.SIGNAL,
                        Post.signal_id.is_not(None)
                    ).order_by(Post.created_at)
                )
                all_posts = all_posts_result.scalars().all()
                
                # Track seen combinations and duplicates to delete
                seen_combinations = set()
                duplicates_to_delete = []
                
                for post in all_posts:
                    # Create a unique key based on channel_id, signal_id, and type
                    post_key = (post.channel_id, post.signal_id, post.type.value)
                    
                    if post_key in seen_combinations:
                        duplicates_to_delete.append(post.id)
                        logger.info(f"Marking duplicate signal post {post.id} for deletion (channel: {post.channel_id}, signal: {post.signal_id})")
                    else:
                        seen_combinations.add(post_key)
                
                # Delete duplicates
                deleted_count = 0
                for post_id in duplicates_to_delete:
                    db.execute(delete(Post).where(Post.id == post_id))
                    deleted_count += 1
                
                if deleted_count > 0:
                    db.commit()
                    logger.info(f"Deleted {deleted_count} duplicate signal posts from SQLite database")
                else:
                    logger.info("No duplicate signal posts found in SQLite database")
                    
            else:
                # For PostgreSQL, use a more efficient approach with window functions
                duplicate_ids_query = """
                WITH duplicates AS (
                    SELECT id,
                           ROW_NUMBER() OVER (
                               PARTITION BY channel_id, signal_id, type
                               ORDER BY created_at
                           ) as row_num
                    FROM posts
                    WHERE type = 'signal' AND signal_id IS NOT NULL
                )
                SELECT id FROM duplicates WHERE row_num > 1
                """

                duplicate_ids_result = await db.execute(text(duplicate_ids_query))
                duplicate_ids = [row[0] for row in duplicate_ids_result.fetchall()]

                deleted_count = 0
                if duplicate_ids:
                    logger.info(f"Found {len(duplicate_ids)} duplicate signal posts to delete")
                    delete_query = delete(Post).where(Post.id.in_(duplicate_ids))
                    result = await db.execute(delete_query)
                    deleted_count = result.rowcount

                    if deleted_count > 0:
                        await db.commit()
                        logger.info(f"Deleted {deleted_count} duplicate signal posts from PostgreSQL database")
                    else:
                        logger.info("No duplicate signal posts found in PostgreSQL database")
                else:
                    logger.info("No duplicate signal posts found in PostgreSQL database")
            
            return deleted_count if 'deleted_count' in locals() else 0
            
        except Exception as e:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
            logger.error(f"Error deleting duplicate signal posts: {e}", exc_info=True)
            raise


async def cleanup_all_duplicate_posts():
    """
    Clean up all types of duplicate posts
    """
    logger.info("Starting comprehensive duplicate post cleanup...")
    
    try:
        signal_deleted = await delete_duplicate_signal_posts()
        performance_deleted = await delete_duplicate_performance_posts()
        
        total_deleted = signal_deleted + performance_deleted
        logger.info(f"Cleanup completed. Total posts deleted: {total_deleted} (Signals: {signal_deleted}, Performance: {performance_deleted})")
        
        return total_deleted
        
    except Exception as e:
        logger.error(f"Error during comprehensive cleanup: {e}", exc_info=True)
        raise


async def get_duplicate_posts_count():
    """
    Get the count of duplicate posts without deleting them.
    Useful for reporting purposes.
    """
    try:
        async for db in get_async_db():
            if is_sqlite_db():
                # For SQLite, count duplicates manually
                all_posts_result = db.execute(
                    select(Post).where(
                        Post.signal_id.is_not(None)
                    )
                )
                all_posts = all_posts_result.scalars().all()
                
                seen_combinations = set()
                duplicate_count = 0
                
                for post in all_posts:
                    post_key = (post.channel_id, post.signal_id, post.type.value)
                    
                    if post_key in seen_combinations:
                        duplicate_count += 1
                    else:
                        seen_combinations.add(post_key)
                
                return duplicate_count
                
            else:
                # For PostgreSQL, use window functions
                duplicate_count_query = """
                WITH duplicates AS (
                    SELECT id,
                           ROW_NUMBER() OVER (
                               PARTITION BY channel_id, signal_id, type
                               ORDER BY created_at
                           ) as row_num
                    FROM posts
                    WHERE signal_id IS NOT NULL
                )
                SELECT COUNT(*) FROM duplicates WHERE row_num > 1
                """

                result = await db.execute(text(duplicate_count_query))
                return result.scalar() or 0
                
    except Exception as e:
        logger.error(f"Error counting duplicate posts: {e}", exc_info=True)
        return 0


if __name__ == "__main__":
    import asyncio
    
    async def main():
        count = await get_duplicate_posts_count()
        print(f"Found {count} duplicate posts")
        
        if count > 0:
            deleted = await cleanup_all_duplicate_posts()
            print(f"Deleted {deleted} duplicate posts")
    
    asyncio.run(main())
