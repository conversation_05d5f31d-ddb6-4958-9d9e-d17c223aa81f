// MignalyBot Catalog - Main JavaScript

// Language switching functionality
let currentLanguage = 'en';

// Language data
const translations = {
    en: {
        dir: 'ltr',
        font: 'Inter'
    },
    fa: {
        dir: 'rtl',
        font: 'Vazirmatn'
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
    initializePricingToggle();
    initializeFAQ();
    initializeContactForm();
    initializeSmoothScrolling();
    initializeNavbarScroll();
});

// Language switching
function switchLanguage(lang) {
    currentLanguage = lang;
    
    // Update active language button
    document.querySelectorAll('.lang-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.lang === lang) {
            btn.classList.add('active');
        }
    });
    
    // Update document direction and font
    const html = document.documentElement;
    const body = document.body;
    
    html.setAttribute('dir', translations[lang].dir);
    html.setAttribute('lang', lang);
    body.setAttribute('dir', translations[lang].dir);
    
    // Update all translatable elements
    document.querySelectorAll('[data-en]').forEach(element => {
        const translation = element.getAttribute(`data-${lang}`);
        if (translation) {
            if (element.tagName === 'INPUT' && element.type === 'text') {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        }
    });
    
    // Update select options
    document.querySelectorAll('option[data-en]').forEach(option => {
        const translation = option.getAttribute(`data-${lang}`);
        if (translation) {
            option.textContent = translation;
        }
    });
    
    // Store language preference
    localStorage.setItem('mignalybot-language', lang);
}

// Initialize language from localStorage or default to English
function initializeLanguage() {
    const savedLanguage = localStorage.getItem('mignalybot-language') || 'en';
    switchLanguage(savedLanguage);
}

// Pricing toggle functionality
function initializePricingToggle() {
    const toggle = document.getElementById('pricing-toggle');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const yearlyPrices = document.querySelectorAll('.yearly-price');
    const yearlyNotes = document.querySelectorAll('.yearly-note');
    
    if (toggle) {
        toggle.addEventListener('change', function() {
            if (this.checked) {
                // Show yearly pricing
                monthlyPrices.forEach(price => price.classList.add('hidden'));
                yearlyPrices.forEach(price => price.classList.remove('hidden'));
                yearlyNotes.forEach(note => note.classList.remove('hidden'));
            } else {
                // Show monthly pricing
                monthlyPrices.forEach(price => price.classList.remove('hidden'));
                yearlyPrices.forEach(price => price.classList.add('hidden'));
                yearlyNotes.forEach(note => note.classList.add('hidden'));
            }
        });
    }
}

// FAQ accordion functionality
function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', function() {
            const isActive = item.classList.contains('active');
            
            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('active');
            });
            
            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('active');
            }
        });
    });
}

// Contact form functionality
function initializeContactForm() {
    const form = document.getElementById('contact-form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);
            
            // Basic validation
            if (!data.name || !data.email) {
                alert(currentLanguage === 'en' ? 'Please fill in all required fields.' : 'لطفاً تمام فیلدهای ضروری را پر کنید.');
                return;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                alert(currentLanguage === 'en' ? 'Please enter a valid email address.' : 'لطفاً یک آدرس ایمیل معتبر وارد کنید.');
                return;
            }
            
            // Simulate form submission
            const submitButton = form.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            
            submitButton.textContent = currentLanguage === 'en' ? 'Sending...' : 'در حال ارسال...';
            submitButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                alert(currentLanguage === 'en' ? 
                    'Thank you for your message! We will get back to you soon.' : 
                    'از پیام شما متشکریم! به زودی با شما تماس خواهیم گرفت.');
                
                form.reset();
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }, 2000);
        });
    }
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Navbar scroll effect
function initializeNavbarScroll() {
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });
}

// Intersection Observer for animations
function initializeAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.feature-card, .pricing-card, .tech-card, .testimonial-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Initialize animations after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAnimations, 500);
});

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Handle window resize
window.addEventListener('resize', debounce(function() {
    // Recalculate any layout-dependent features
    console.log('Window resized');
}, 250));

// Export functions for global access
window.switchLanguage = switchLanguage;
