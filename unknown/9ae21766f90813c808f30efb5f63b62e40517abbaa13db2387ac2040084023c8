FROM python:3.10-slim

WORKDIR /app

# Install system dependencies including fonts
RUN apt-get update && apt-get install -y --no-install-recommends \
    sqlite3 \
    fonts-dejavu-core \
    fonts-liberation \
    fontconfig \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install --no-cache-dir --timeout 300 --retries 5 --resume-retries 5 -r requirements.txt

# Copy project files
COPY . .

# Create necessary directories with proper permissions
RUN mkdir -p data logs images/pnl src/admin/static src/admin/templates && \
    chmod 755 data logs images src/admin/static src/admin/templates

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PORT=9000
ENV DOCKER_ENV=true

# Expose port
EXPOSE 9000

# Initialize database and start full application with all schedulers
CMD ["sh", "-c", "python init_simple.py && python main.py"]
