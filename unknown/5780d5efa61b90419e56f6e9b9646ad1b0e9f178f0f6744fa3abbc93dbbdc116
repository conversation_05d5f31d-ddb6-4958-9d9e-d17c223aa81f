"""
Configuration settings for MignalyBot v2

This module provides a centralized configuration system with:
- Environment variable loading
- Type validation
- Default values
- Performance optimization settings
"""

import os
from typing import List, Optional
from dataclasses import dataclass, field
from pathlib import Path

from dotenv import load_dotenv


@dataclass
class DatabaseSettings:
    """Database configuration settings"""
    url: str = "sqlite:///mignalybot_v2.db"
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 60
    pool_recycle: int = 300
    pool_pre_ping: bool = True
    echo: bool = False
    
    @property
    def is_sqlite(self) -> bool:
        return self.url.startswith("sqlite:")
    
    @property
    def async_url(self) -> str:
        """Convert database URL to async format if needed"""
        if self.url.startswith("postgresql://"):
            return self.url.replace("postgresql://", "postgresql+asyncpg://")
        elif self.url.startswith("mysql://"):
            return self.url.replace("mysql://", "mysql+aiomysql://")
        return self.url


@dataclass
class TelegramSettings:
    """Telegram bot configuration settings"""
    bot_token: str = ""
    webhook_url: Optional[str] = None
    webhook_secret: Optional[str] = None
    max_message_length: int = 4096
    chunk_size: int = 3000
    rate_limit_messages: int = 30
    rate_limit_window: int = 60
    
    @property
    def is_webhook_mode(self) -> bool:
        return bool(self.webhook_url)


@dataclass
class AISettings:
    """AI integration configuration settings"""
    qwen_api_key: str = ""
    qwen_endpoint: str = "https://dashscope-intl.aliyuncs.com/compatible-mode/v1"
    qwen_model: str = "qwen-max-2025-01-25"
    max_tokens: int = 4000
    temperature: float = 0.7
    cache_ttl: int = 3600  # 1 hour
    rate_limit_requests: int = 100
    rate_limit_window: int = 60
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class DataCollectionSettings:
    """Data collection configuration settings"""
    workers: int = 5
    batch_size: int = 100
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 2.0
    symbols: List[str] = field(default_factory=lambda: ["BTC/USD", "ETH/USD", "EUR/USD", "GBP/USD"])
    timeframes: List[str] = field(default_factory=lambda: ["1h", "4h", "1d"])
    enable_news: bool = True
    enable_signals: bool = True
    enable_calendar: bool = True
    collection_interval: int = 300  # 5 minutes


@dataclass
class AdminSettings:
    """Admin interface configuration settings"""
    host: str = "0.0.0.0"
    port: int = 8000
    username: str = "admin"
    password: str = "admin123"
    session_timeout: int = 3600
    enable_metrics: bool = True
    cors_origins: List[str] = field(default_factory=lambda: ["*"])


@dataclass
class LoggingSettings:
    """Logging configuration settings"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_performance_logs: bool = False


@dataclass
class PerformanceSettings:
    """Performance optimization settings"""
    enable_metrics: bool = True
    metrics_interval: int = 60
    memory_threshold: float = 0.8  # 80% memory usage threshold
    cpu_threshold: float = 0.8     # 80% CPU usage threshold
    gc_threshold: int = 1000       # Garbage collection threshold
    enable_profiling: bool = False


class Settings:
    """Main settings class that loads and manages all configuration"""
    
    def __init__(self, env_file: Optional[str] = None):
        # Load environment variables
        if env_file:
            load_dotenv(env_file)
        else:
            # Try to load from multiple locations
            for env_path in [".env", "../.env", "../../.env"]:
                if Path(env_path).exists():
                    load_dotenv(env_path)
                    break
        
        # Initialize all settings
        self.database = self._load_database_settings()
        self.telegram = self._load_telegram_settings()
        self.ai = self._load_ai_settings()
        self.data_collection = self._load_data_collection_settings()
        self.admin = self._load_admin_settings()
        self.logging = self._load_logging_settings()
        self.performance = self._load_performance_settings()
        
        # General settings
        self.timezone = os.getenv("TIMEZONE", "Asia/Tehran")
        self.default_language = os.getenv("DEFAULT_LANGUAGE", "en")
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
    
    def _load_database_settings(self) -> DatabaseSettings:
        return DatabaseSettings(
            url=os.getenv("DATABASE_URL", "sqlite:///mignalybot_v2.db"),
            pool_size=int(os.getenv("DB_POOL_SIZE", "10")),
            max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "20")),
            pool_timeout=int(os.getenv("DB_POOL_TIMEOUT", "60")),
            pool_recycle=int(os.getenv("DB_POOL_RECYCLE", "300")),
            pool_pre_ping=os.getenv("DB_POOL_PRE_PING", "true").lower() == "true",
            echo=os.getenv("DB_ECHO", "false").lower() == "true"
        )
    
    def _load_telegram_settings(self) -> TelegramSettings:
        return TelegramSettings(
            bot_token=os.getenv("TELEGRAM_BOT_TOKEN", ""),
            webhook_url=os.getenv("TELEGRAM_WEBHOOK_URL"),
            webhook_secret=os.getenv("TELEGRAM_WEBHOOK_SECRET"),
            max_message_length=int(os.getenv("TELEGRAM_MAX_MESSAGE_LENGTH", "4096")),
            chunk_size=int(os.getenv("TELEGRAM_CHUNK_SIZE", "3000")),
            rate_limit_messages=int(os.getenv("TELEGRAM_RATE_LIMIT_MESSAGES", "30")),
            rate_limit_window=int(os.getenv("TELEGRAM_RATE_LIMIT_WINDOW", "60"))
        )
    
    def _load_ai_settings(self) -> AISettings:
        return AISettings(
            qwen_api_key=os.getenv("QWEN_API_KEY", ""),
            qwen_endpoint=os.getenv("QWEN_ENDPOINT", "https://dashscope-intl.aliyuncs.com/compatible-mode/v1"),
            qwen_model=os.getenv("QWEN_MODEL", "qwen-max-2025-01-25"),
            max_tokens=int(os.getenv("AI_MAX_TOKENS", "4000")),
            temperature=float(os.getenv("AI_TEMPERATURE", "0.7")),
            cache_ttl=int(os.getenv("AI_CACHE_TTL", "3600")),
            rate_limit_requests=int(os.getenv("AI_RATE_LIMIT_REQUESTS", "100")),
            rate_limit_window=int(os.getenv("AI_RATE_LIMIT_WINDOW", "60")),
            timeout=int(os.getenv("AI_TIMEOUT", "30")),
            max_retries=int(os.getenv("AI_MAX_RETRIES", "3")),
            retry_delay=float(os.getenv("AI_RETRY_DELAY", "1.0"))
        )
    
    def _load_data_collection_settings(self) -> DataCollectionSettings:
        symbols_str = os.getenv("SYMBOLS", "BTC/USD,ETH/USD,EUR/USD,GBP/USD")
        timeframes_str = os.getenv("TIMEFRAMES", "1h,4h,1d")
        
        return DataCollectionSettings(
            workers=int(os.getenv("DATA_COLLECTION_WORKERS", "5")),
            batch_size=int(os.getenv("DATA_COLLECTION_BATCH_SIZE", "100")),
            timeout=int(os.getenv("DATA_COLLECTION_TIMEOUT", "30")),
            retry_attempts=int(os.getenv("DATA_COLLECTION_RETRY_ATTEMPTS", "3")),
            retry_delay=float(os.getenv("DATA_COLLECTION_RETRY_DELAY", "2.0")),
            symbols=[s.strip() for s in symbols_str.split(",")],
            timeframes=[t.strip() for t in timeframes_str.split(",")],
            enable_news=os.getenv("ENABLE_NEWS", "true").lower() == "true",
            enable_signals=os.getenv("ENABLE_SIGNALS", "true").lower() == "true",
            enable_calendar=os.getenv("ENABLE_CALENDAR", "true").lower() == "true",
            collection_interval=int(os.getenv("DATA_COLLECTION_INTERVAL", "300"))
        )
    
    def _load_admin_settings(self) -> AdminSettings:
        cors_origins_str = os.getenv("ADMIN_CORS_ORIGINS", "*")
        cors_origins = [o.strip() for o in cors_origins_str.split(",")]
        
        return AdminSettings(
            host=os.getenv("ADMIN_HOST", "0.0.0.0"),
            port=int(os.getenv("ADMIN_PORT", "8000")),
            username=os.getenv("ADMIN_USERNAME", "admin"),
            password=os.getenv("ADMIN_PASSWORD", "admin123"),
            session_timeout=int(os.getenv("ADMIN_SESSION_TIMEOUT", "3600")),
            enable_metrics=os.getenv("ADMIN_ENABLE_METRICS", "true").lower() == "true",
            cors_origins=cors_origins
        )
    
    def _load_logging_settings(self) -> LoggingSettings:
        return LoggingSettings(
            level=os.getenv("LOG_LEVEL", "INFO"),
            format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            file_path=os.getenv("LOG_FILE_PATH"),
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5")),
            enable_performance_logs=os.getenv("LOG_ENABLE_PERFORMANCE", "false").lower() == "true"
        )
    
    def _load_performance_settings(self) -> PerformanceSettings:
        return PerformanceSettings(
            enable_metrics=os.getenv("ENABLE_PERFORMANCE_METRICS", "true").lower() == "true",
            metrics_interval=int(os.getenv("PERFORMANCE_METRICS_INTERVAL", "60")),
            memory_threshold=float(os.getenv("PERFORMANCE_MEMORY_THRESHOLD", "0.8")),
            cpu_threshold=float(os.getenv("PERFORMANCE_CPU_THRESHOLD", "0.8")),
            gc_threshold=int(os.getenv("PERFORMANCE_GC_THRESHOLD", "1000")),
            enable_profiling=os.getenv("ENABLE_PROFILING", "false").lower() == "true"
        )
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        if not self.telegram.bot_token:
            errors.append("TELEGRAM_BOT_TOKEN is required")
        
        if not self.ai.qwen_api_key:
            errors.append("QWEN_API_KEY is required")
        
        if self.database.pool_size <= 0:
            errors.append("DB_POOL_SIZE must be greater than 0")
        
        if self.data_collection.workers <= 0:
            errors.append("DATA_COLLECTION_WORKERS must be greater than 0")
        
        return errors
