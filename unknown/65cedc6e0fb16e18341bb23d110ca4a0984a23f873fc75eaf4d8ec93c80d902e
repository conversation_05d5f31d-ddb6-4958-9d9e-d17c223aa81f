"""
Event Result Scheduler for MignalyBot
Runs every 15 minutes to check for event results and signal updates
"""

import asyncio
import logging
from datetime import datetime, timedelta

from src.ai_integration.event_result_processor import process_event_results
from src.ai_integration.signal_update_processor import process_signal_updates
from src.utils.helpers import get_current_time

logger = logging.getLogger(__name__)

async def run_event_result_scheduler():
    """
    Run the event result scheduler
    Checks for event results and signal updates every 15 minutes
    """
    logger.info("Starting event result and signal update scheduler...")

    while True:
        try:
            # Get current time
            now = get_current_time()
            logger.info(f"Running event result and signal update check at {now.strftime('%Y-%m-%d %H:%M:%S %Z')}")

            # Process event results
            logger.info("📅 Processing event results...")
            await process_event_results()
            logger.info("✅ Event results processing completed")

            # Process signal updates
            logger.info("📊 Processing signal updates...")
            await process_signal_updates()
            logger.info("✅ Signal updates processing completed")

            # Wait 15 minutes before next check
            logger.info("Event result and signal update check completed. Waiting 15 minutes for next check...")
            await asyncio.sleep(15 * 60)  # 15 minutes

        except Exception as e:
            logger.error(f"Error in event result and signal update scheduler: {e}", exc_info=True)
            # Wait 5 minutes before retrying on error
            await asyncio.sleep(5 * 60)

def get_next_event_result_check_time():
    """
    Get the next scheduled event result check time
    
    Returns:
        datetime: Next check time
    """
    now = get_current_time()
    # Next check is in 15 minutes
    next_check = now + timedelta(minutes=15)
    return next_check

def get_time_until_next_event_result_check():
    """
    Get the time until the next event result check
    
    Returns:
        timedelta: Time until next check
    """
    now = get_current_time()
    next_check = get_next_event_result_check_time()
    return next_check - now
