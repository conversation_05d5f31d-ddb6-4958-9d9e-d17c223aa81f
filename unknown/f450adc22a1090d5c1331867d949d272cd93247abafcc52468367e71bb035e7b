"""
Data collection scheduler for MignalyBot
"""

import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy import select

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Config
from src.data_collection.market_data import collect_market_data
from src.data_collection.news import collect_news
from src.data_collection.economic_calendar import collect_economic_events
from src.strategies.processor import process_strategies
from src.ai_integration.event_notifier import check_event_notifications
from src.ai_integration.content_generator import generate_content

logger = logging.getLogger(__name__)

async def data_collection_task():
    """Main data collection task that runs periodically"""
    try:
        logger.info("🚀 Starting comprehensive data collection task")

        # Get configuration
        async for db in get_async_db():
            logger.debug("📋 Loading configuration from database...")

            # Handle SQLite differently than other databases
            if is_sqlite_db():
                config_result = db.execute(select(Config))
            else:
                config_result = await db.execute(select(Config))

            config = config_result.scalars().first()

            if not config:
                logger.error("❌ No configuration found in database")
                return

            symbols = [s.strip() for s in config.symbols.split(",")]
            timeframes = [t.strip() for t in config.timeframes.split(",")]

            logger.info(f"📊 Configuration loaded - Symbols: {symbols}, Timeframes: {timeframes}")
            logger.info(f"🔧 Features enabled - News: {config.enable_news}, Calendar: {config.enable_calendar}, Signals: {config.enable_signals}")

            # Collect market data
            logger.info("📈 Starting market data collection...")
            await collect_market_data(symbols, timeframes)
            logger.info("✅ Market data collection completed")

            # Collect news if enabled
            if config.enable_news:
                logger.info("📰 Starting news collection...")
                await collect_news(symbols)
                logger.info("✅ News collection completed")
            else:
                logger.info("⏭️ News collection disabled")

            # Collect economic calendar events if enabled
            if config.enable_calendar:
                logger.info("📅 Starting economic calendar collection...")
                await collect_economic_events()
                logger.info("✅ Economic calendar collection completed")
            else:
                logger.info("⏭️ Economic calendar collection disabled")

            # Process strategies if enabled
            if config.enable_signals:
                logger.info("🎯 Starting strategy processing...")
                await process_strategies()
                logger.info("✅ Strategy processing completed")
            else:
                logger.info("⏭️ Strategy processing disabled")

            # Check for event notifications (run every time)
            logger.info("🔔 Checking event notifications...")
            await check_event_notifications()
            logger.info("✅ Event notifications check completed")

            # Generate content for all channels (all post types including greeting)
            logger.info("🤖 Starting content generation...")
            posts_created = await generate_content()
            logger.info(f"✅ Content generation completed. Created {posts_created} posts")

            logger.info("🎉 Data collection task completed successfully")

    except Exception as e:
        logger.error(f"💥 Error in data collection task: {e}", exc_info=True)

async def start_data_collection():
    """Start the data collection scheduler - runs every 6 hours"""
    logger.info("Starting data collection scheduler (6-hour intervals)")

    while True:
        try:
            # Run data collection task
            await data_collection_task()

            # Wait for next collection time (6 hours)
            logger.info("Next data collection in 6 hours")
            await asyncio.sleep(6 * 3600)  # 6 hours = 21600 seconds

        except Exception as e:
            logger.error(f"Error in data collection scheduler: {e}", exc_info=True)
            # Wait 30 minutes before retrying on error
            await asyncio.sleep(1800)
