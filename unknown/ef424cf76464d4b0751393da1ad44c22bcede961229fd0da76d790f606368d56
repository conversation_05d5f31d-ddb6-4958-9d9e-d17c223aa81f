{% extends "base.html" %}

{% block title %}Prompt Templates - MignalyBot Admin{% endblock %}

{% block page_title %}Prompt Templates{% endblock %}

{% block extra_css %}
<style>
    .prompt-card {
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .prompt-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .prompt-card .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .prompt-card .badge-container {
        margin-top: 10px;
    }
    
    .prompt-card .badge {
        margin-right: 5px;
    }
    
    .template-content {
        max-height: 200px;
        overflow-y: auto;
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        white-space: pre-wrap;
    }
    
    .post-type-badge {
        font-size: 0.8em;
        padding: 4px 8px;
    }
    
    .language-badge {
        font-size: 0.8em;
        padding: 4px 8px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Prompt Templates</h5>
                <button type="button" class="btn btn-primary" id="addPromptBtn">
                    <i class="fas fa-plus"></i> Add Template
                </button>
            </div>
            <div class="card-body">
                <div class="row" id="promptsContainer">
                    <!-- Prompt templates will be loaded here -->
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Post Types</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-newspaper text-primary"></i> News</span>
                        <span class="badge bg-primary rounded-pill" id="newsCount">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-calendar-alt text-info"></i> Events</span>
                        <span class="badge bg-info rounded-pill" id="eventsCount">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-signal text-success"></i> Signals</span>
                        <span class="badge bg-success rounded-pill" id="signalsCount">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-chart-line text-warning"></i> Market Analysis</span>
                        <span class="badge bg-warning rounded-pill" id="marketAnalysisCount">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-clock text-secondary"></i> Countdown</span>
                        <span class="badge bg-secondary rounded-pill" id="countdownCount">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-sun text-warning"></i> Greeting</span>
                        <span class="badge bg-warning rounded-pill" id="greetingCount">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-chart-bar text-danger"></i> Signal Update</span>
                        <span class="badge bg-danger rounded-pill" id="signalUpdateCount">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-exclamation-circle text-dark"></i> Event Result</span>
                        <span class="badge bg-dark rounded-pill" id="eventResultCount">0</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" id="testPromptBtn">
                        <i class="fas fa-flask"></i> Test Prompt
                    </button>
                    <button class="btn btn-outline-success" id="exportPromptsBtn">
                        <i class="fas fa-download"></i> Export Templates
                    </button>
                    <button class="btn btn-outline-info" id="importPromptsBtn">
                        <i class="fas fa-upload"></i> Import Templates
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Prompt Modal -->
<div class="modal fade" id="promptModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promptModalTitle">Add Prompt Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="promptForm">
                    <input type="hidden" id="promptId" name="id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="postType" class="form-label">Post Type</label>
                                <select class="form-select" id="postType" name="post_type" required>
                                    <option value="">Select post type...</option>
                                    <option value="news">News</option>
                                    <option value="events">Events</option>
                                    <option value="signals">Signals</option>
                                    <option value="market_analysis">Market Analysis</option>
                                    <option value="countdown">Countdown</option>
                                    <option value="greeting">Greeting</option>
                                    <option value="signal_update">Signal Update</option>
                                    <option value="event_result">Event Result</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label">Language</label>
                                <select class="form-select" id="language" name="language" required>
                                    <option value="fa">Farsi (فارسی)</option>
                                    <option value="en">English</option>
                                    <option value="ar">Arabic (العربية)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="description" name="description" 
                               placeholder="Brief description of this template...">
                    </div>
                    
                    <div class="mb-3">
                        <label for="templateContent" class="form-label">Template Content</label>
                        <textarea class="form-control" id="templateContent" name="template_content" 
                                  rows="15" required placeholder="Enter your prompt template here..."></textarea>
                        <div class="form-text">
                            Use placeholders like {symbol}, {title}, {content}, {channel_brand}, etc. 
                            These will be replaced with actual values when generating content.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="active" name="active" checked>
                            <label class="form-check-label" for="active">
                                Active
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="savePromptBtn">Save Template</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Prompt Modal -->
<div class="modal fade" id="testPromptModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Prompt Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="testPostType" class="form-label">Select Template</label>
                    <select class="form-select" id="testPostType">
                        <option value="">Select template to test...</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="testPromptContent" class="form-label">Prompt Content</label>
                    <textarea class="form-control" id="testPromptContent" rows="10" readonly></textarea>
                </div>
                <div class="mb-3">
                    <label for="testResult" class="form-label">AI Response</label>
                    <div class="border rounded p-3 bg-light" id="testResult" style="min-height: 200px; max-height: 400px; overflow-y: auto;">
                        Select a template and click "Test" to see AI response...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="runTestBtn">Test Template</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let currentPrompts = [];

    // Load prompt templates
    function loadPrompts() {
        $.get('/api/prompt-templates')
            .done(function(data) {
                currentPrompts = data;
                displayPrompts(data);
                updateCounts(data);
            })
            .fail(function(xhr) {
                console.error('Error loading prompts:', xhr);
                showAlert('Error loading prompt templates', 'danger');
            });
    }

    // Display prompts in cards
    function displayPrompts(prompts) {
        const container = $('#promptsContainer');
        container.empty();

        if (prompts.length === 0) {
            container.html(`
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-edit fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No prompt templates found</h5>
                        <p class="text-muted">Create your first prompt template to get started.</p>
                        <button class="btn btn-primary" id="addFirstPromptBtn">
                            <i class="fas fa-plus"></i> Add First Template
                        </button>
                    </div>
                </div>
            `);
            return;
        }

        prompts.forEach(function(prompt) {
            const postTypeIcon = getPostTypeIcon(prompt.post_type);
            const languageFlag = getLanguageFlag(prompt.language);

            const card = `
                <div class="col-md-6 mb-3">
                    <div class="card prompt-card" data-id="${prompt.id}">
                        <div class="card-header">
                            <div>
                                <h6 class="mb-0">
                                    <i class="${postTypeIcon}"></i> ${prompt.post_type.replace('_', ' ').toUpperCase()}
                                </h6>
                                <small class="text-muted">${prompt.description || 'No description'}</small>
                            </div>
                            <div>
                                <span class="badge bg-primary language-badge">${languageFlag} ${prompt.language.toUpperCase()}</span>
                                ${prompt.active ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-secondary">Inactive</span>'}
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="template-content">${prompt.template_content.substring(0, 200)}${prompt.template_content.length > 200 ? '...' : ''}</div>
                            <div class="mt-3">
                                <button class="btn btn-sm btn-outline-primary edit-prompt" data-id="${prompt.id}">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-sm btn-outline-success test-prompt" data-id="${prompt.id}">
                                    <i class="fas fa-flask"></i> Test
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-prompt" data-id="${prompt.id}">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.append(card);
        });
    }

    // Update counts in sidebar
    function updateCounts(prompts) {
        const counts = {};
        prompts.forEach(function(prompt) {
            counts[prompt.post_type] = (counts[prompt.post_type] || 0) + 1;
        });

        $('#newsCount').text(counts.news || 0);
        $('#eventsCount').text(counts.events || 0);
        $('#signalsCount').text(counts.signals || 0);
        $('#marketAnalysisCount').text(counts.market_analysis || 0);
        $('#countdownCount').text(counts.countdown || 0);
        $('#greetingCount').text(counts.greeting || 0);
        $('#signalUpdateCount').text(counts.signal_update || 0);
        $('#eventResultCount').text(counts.event_result || 0);
    }

    // Get icon for post type
    function getPostTypeIcon(postType) {
        const icons = {
            'news': 'fas fa-newspaper',
            'events': 'fas fa-calendar-alt',
            'signals': 'fas fa-signal',
            'market_analysis': 'fas fa-chart-line',
            'countdown': 'fas fa-clock',
            'greeting': 'fas fa-sun',
            'signal_update': 'fas fa-chart-bar',
            'event_result': 'fas fa-exclamation-circle'
        };
        return icons[postType] || 'fas fa-file-alt';
    }

    // Get flag for language
    function getLanguageFlag(language) {
        const flags = {
            'fa': '🇮🇷',
            'en': '🇺🇸',
            'ar': '🇸🇦'
        };
        return flags[language] || '🌍';
    }

    // Show alert
    function showAlert(message, type = 'info') {
        const alert = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container-fluid').prepend(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }

    // Add prompt button click
    $(document).on('click', '#addPromptBtn, #addFirstPromptBtn', function() {
        $('#promptModalTitle').text('Add Prompt Template');
        $('#promptForm')[0].reset();
        $('#promptId').val('');
        $('#language').val('fa'); // Default to Farsi
        $('#promptModal').modal('show');
    });

    // Edit prompt button click
    $(document).on('click', '.edit-prompt', function() {
        const promptId = $(this).data('id');
        const prompt = currentPrompts.find(p => p.id === promptId);

        if (prompt) {
            $('#promptModalTitle').text('Edit Prompt Template');
            $('#promptId').val(prompt.id);
            $('#postType').val(prompt.post_type);
            $('#language').val(prompt.language);
            $('#description').val(prompt.description || '');
            $('#templateContent').val(prompt.template_content);
            $('#active').prop('checked', prompt.active);
            $('#promptModal').modal('show');
        }
    });

    // Save prompt
    $('#savePromptBtn').click(function() {
        const formData = {
            post_type: $('#postType').val(),
            language: $('#language').val(),
            template_content: $('#templateContent').val(),
            description: $('#description').val(),
            active: $('#active').is(':checked')
        };

        const promptId = $('#promptId').val();
        const isEdit = promptId !== '';

        const url = isEdit ? `/api/prompt-templates/${promptId}` : '/api/prompt-templates';
        const method = isEdit ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            type: method,
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#promptModal').modal('hide');
                showAlert(`Prompt template ${isEdit ? 'updated' : 'created'} successfully!`, 'success');
                loadPrompts();
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.detail || 'An error occurred';
                showAlert(`Error: ${error}`, 'danger');
            }
        });
    });

    // Delete prompt
    $(document).on('click', '.delete-prompt', function() {
        const promptId = $(this).data('id');
        const prompt = currentPrompts.find(p => p.id === promptId);

        if (confirm(`Are you sure you want to delete the ${prompt.post_type} template for ${prompt.language}?`)) {
            $.ajax({
                url: `/api/prompt-templates/${promptId}`,
                type: 'DELETE',
                success: function() {
                    showAlert('Prompt template deleted successfully!', 'success');
                    loadPrompts();
                },
                error: function(xhr) {
                    const error = xhr.responseJSON?.detail || 'An error occurred';
                    showAlert(`Error: ${error}`, 'danger');
                }
            });
        }
    });

    // Test prompt functionality
    $('#testPromptBtn').click(function() {
        // Populate test modal with available templates
        const select = $('#testPostType');
        select.empty().append('<option value="">Select template to test...</option>');

        currentPrompts.forEach(function(prompt) {
            const option = `<option value="${prompt.id}">${prompt.post_type} (${prompt.language})</option>`;
            select.append(option);
        });

        $('#testPromptModal').modal('show');
    });

    // Test template selection change
    $('#testPostType').change(function() {
        const promptId = $(this).val();
        if (promptId) {
            const prompt = currentPrompts.find(p => p.id == promptId);
            if (prompt) {
                $('#testPromptContent').val(prompt.template_content);
            }
        } else {
            $('#testPromptContent').val('');
        }
        $('#testResult').html('Select a template and click "Test" to see AI response...');
    });

    // Run test
    $('#runTestBtn').click(function() {
        const promptContent = $('#testPromptContent').val();
        if (!promptContent) {
            showAlert('Please select a template to test', 'warning');
            return;
        }

        $('#testResult').html('<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>Testing prompt...</div></div>');

        // Use a simple test prompt for demonstration
        const testPrompt = promptContent.replace(/\{[^}]+\}/g, '[TEST_VALUE]');

        $.ajax({
            url: '/api/actions/test-qwen',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ prompt: testPrompt }),
            success: function(response) {
                $('#testResult').html(response.response.replace(/\n/g, '<br>'));
            },
            error: function(xhr) {
                $('#testResult').html('<div class="text-danger">Error: ' + xhr.responseJSON.detail + '</div>');
            }
        });
    });

    // Initial load
    loadPrompts();
});
</script>
{% endblock %}
