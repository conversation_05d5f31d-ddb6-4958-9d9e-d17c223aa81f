"""
Core database models for MignalyBot v2

Includes optimized models for:
- Configuration
- Channels
- Strategies
- Posts
"""

from datetime import datetime
from enum import Enum as PyEnum
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Float, Boolean, DateTime, Text, 
    ForeignKey, Enum, JSON, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
import pytz

from .base import Base


class PostType(PyEnum):
    """Post type enumeration"""
    NEWS = "news"
    SIGNAL = "signal"
    ANALYSIS = "analysis"
    EVENT = "event"
    GREETING = "greeting"


class PostStatus(PyEnum):
    """Post status enumeration"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    PUBLISHED = "published"
    FAILED = "failed"


class SignalStatus(PyEnum):
    """Trading signal status enumeration"""
    ACTIVE = "active"
    TP1_HIT = "tp1_hit"
    TP2_HIT = "tp2_hit"
    TP3_HIT = "tp3_hit"
    ALL_TP_HIT = "all_tp_hit"
    SL_HIT = "sl_hit"
    BREAK_EVEN = "break_even"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    # Legacy status for backward compatibility
    TP_HIT = "tp_hit"


class Config(Base):
    """Application configuration model"""
    __tablename__ = "configs"
    
    # AI Configuration
    qwen_api_key = Column(String(255), nullable=True)
    qwen_endpoint = Column(String(500), default="https://dashscope-intl.aliyuncs.com/compatible-mode/v1")
    qwen_model = Column(String(100), default="qwen-max-2025-01-25")
    
    # General Settings
    default_language = Column(String(10), default="en", index=True)
    timezone = Column(String(50), default="Asia/Tehran")
    
    # Data Collection Settings
    symbols = Column(String(500), default="BTC/USD,ETH/USD,EUR/USD,GBP/USD")
    timeframes = Column(String(255), default="1h,4h,1d")
    collection_interval = Column(Integer, default=300)  # seconds
    
    # Feature Flags
    enable_news = Column(Boolean, default=True, index=True)
    enable_signals = Column(Boolean, default=True, index=True)
    enable_calendar = Column(Boolean, default=True, index=True)
    enable_ai_strategies = Column(Boolean, default=True, index=True)
    
    # Performance Settings
    max_tokens_per_request = Column(Integer, default=4000)
    ai_cache_ttl = Column(Integer, default=3600)  # seconds
    batch_size = Column(Integer, default=100)
    
    # Unique constraint to ensure only one config row
    __table_args__ = (
        UniqueConstraint('id', name='uq_config_single_row'),
    )


class Channel(Base):
    """Telegram channel model"""
    __tablename__ = "channels"
    
    name = Column(String(255), nullable=False, index=True)
    chat_id = Column(String(100), nullable=False, unique=True, index=True)
    language = Column(String(10), default="en", index=True)
    brand_name = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    
    # Channel settings
    active = Column(Boolean, default=True, index=True)
    post_frequency = Column(Integer, default=1)  # hours
    max_posts_per_day = Column(Integer, default=24)
    
    # Content preferences
    enable_news = Column(Boolean, default=True)
    enable_signals = Column(Boolean, default=True)
    enable_analysis = Column(Boolean, default=True)
    enable_events = Column(Boolean, default=True)
    enable_greetings = Column(Boolean, default=True)
    
    # Relationships
    posts = relationship("Post", back_populates="channel", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_channel_active_language', active, language),
        Index('idx_channel_chat_id_active', chat_id, active),
    )


class Strategy(Base):
    """Trading strategy model"""
    __tablename__ = "strategies"
    
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    strategy_type = Column(String(50), nullable=False, index=True)  # trend_following, mean_reversion, etc.
    
    # Strategy configuration
    symbols = Column(String(500), nullable=False)
    timeframes = Column(String(255), nullable=False)
    parameters = Column(JSON, nullable=True)
    
    # AI-generated strategy code
    code = Column(Text, nullable=False)
    ai_generated = Column(Boolean, default=False, index=True)
    ai_prompt = Column(Text, nullable=True)  # Store the prompt used to generate the strategy
    
    # Strategy metadata
    active = Column(Boolean, default=True, index=True)
    performance_score = Column(Float, default=0.0, index=True)
    total_signals = Column(Integer, default=0)
    successful_signals = Column(Integer, default=0)
    
    # Market conditions when strategy was created/updated
    market_conditions = Column(JSON, nullable=True)
    last_optimized = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    signals = relationship("TradingSignal", back_populates="strategy", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_strategy_active_type', active, strategy_type),
        Index('idx_strategy_performance', performance_score.desc()),
        Index('idx_strategy_ai_generated', ai_generated, active),
    )
    
    @property
    def success_rate(self) -> float:
        """Calculate strategy success rate"""
        if self.total_signals == 0:
            return 0.0
        return (self.successful_signals / self.total_signals) * 100


class Post(Base):
    """Post model for Telegram messages"""
    __tablename__ = "posts"
    
    # Foreign keys
    channel_id = Column(Integer, ForeignKey("channels.id"), nullable=False, index=True)
    news_id = Column(Integer, ForeignKey("news_items.id"), nullable=True, index=True)
    event_id = Column(Integer, ForeignKey("economic_events.id"), nullable=True, index=True)
    signal_id = Column(Integer, ForeignKey("trading_signals.id"), nullable=True, index=True)
    
    # Post content
    type = Column(Enum(PostType), nullable=False, index=True)
    content = Column(Text, nullable=False)
    status = Column(Enum(PostStatus), default=PostStatus.DRAFT, index=True)
    
    # Scheduling
    scheduled_time = Column(DateTime(timezone=True), nullable=True, index=True)
    published_time = Column(DateTime(timezone=True), nullable=True, index=True)
    
    # Telegram metadata
    message_id = Column(String(50), nullable=True, index=True)
    message_thread_id = Column(String(50), nullable=True)
    
    # Content metadata
    language = Column(String(10), nullable=True, index=True)
    content_hash = Column(String(32), nullable=True, index=True)  # For duplicate detection
    ai_generated = Column(Boolean, default=False, index=True)
    
    # Performance tracking
    views = Column(Integer, default=0)
    reactions = Column(Integer, default=0)
    
    # Relationships
    channel = relationship("Channel", back_populates="posts")
    news_item = relationship("NewsItem", back_populates="posts")
    economic_event = relationship("EconomicEvent", back_populates="posts")
    trading_signal = relationship("TradingSignal", back_populates="posts")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_post_channel_status', channel_id, status),
        Index('idx_post_scheduled_time', scheduled_time),
        Index('idx_post_type_status', type, status),
        Index('idx_post_content_hash', content_hash),
        Index('idx_post_published_time', published_time.desc()),
    )


# Note: Other model modules are imported separately to avoid circular imports
