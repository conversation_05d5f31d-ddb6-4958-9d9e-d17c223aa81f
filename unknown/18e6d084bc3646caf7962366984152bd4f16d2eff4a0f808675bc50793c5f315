"""
Database setup and initialization
"""

import os
import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.pool import NullPool

logger = logging.getLogger(__name__)

# Get database URL from environment variable
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///mignalybot.db")
# Fix any encoding issues in the URL
if '\\x3a' in DATABASE_URL:
    DATABASE_URL = DATABASE_URL.replace('\\x3a', ':')

# Convert SQLite URL to async format if needed
if DATABASE_URL.startswith("sqlite:"):
    # For SQLite, we'll use synchronous operations for simplicity
    # Use NullPool to avoid connection pool issues
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=NullPool  # Use NullPool instead of QueuePool
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    async_engine = None
    AsyncSessionLocal = None
else:
    # For other databases, use async operations
    async_db_url = DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    async_engine = create_async_engine(
        async_db_url,
        pool_size=5,  # Reduce pool size to avoid connection issues
        max_overflow=5,  # Reduce max overflow
        pool_timeout=60,  # Increase timeout
        pool_recycle=300,  # Recycle connections after 5 minutes
        pool_pre_ping=True  # Check connection validity before using
    )
    AsyncSessionLocal = sessionmaker(
        bind=async_engine, class_=AsyncSession, expire_on_commit=False
    )
    engine = create_engine(
        DATABASE_URL,
        pool_size=5,  # Reduce pool size to avoid connection issues
        max_overflow=5,  # Reduce max overflow
        pool_timeout=60,  # Increase timeout
        pool_recycle=300,  # Recycle connections after 5 minutes
        pool_pre_ping=True  # Check connection validity before using
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

async def init_db():
    """Initialize the database, creating tables if they don't exist"""
    from src.database.models import (
        CandleData,
        NewsItem,
        EconomicEvent,
        TradingSignal,
        Strategy,
        Channel,
        Post,
        Config
    )

    logger.info("Initializing database")

    # Create tables
    Base.metadata.create_all(bind=engine)

    # Run migrations
    await run_migrations()

    # Initialize default config if needed
    await init_default_config()

    logger.info("Database initialized successfully")

async def run_migrations():
    """Run database migrations"""
    try:
        logger.info("Running database migrations...")

        # Import and run the multi-TP migration
        from src.database.migrations.add_multi_tp_support import migrate_multi_tp_support
        await migrate_multi_tp_support()

        logger.info("Database migrations completed successfully")
    except Exception as e:
        logger.error(f"Error running migrations: {e}", exc_info=True)
        # Don't raise the error to prevent startup failure
        # The system should still work with legacy schema

async def init_default_config():
    """Initialize default configuration in the database"""
    from src.database.models import Config
    from sqlalchemy import select

    # Check if config exists
    async for session in get_async_db():
        # For SQLite, we need to handle synchronous operations
        if AsyncSessionLocal is None:
            result = session.execute(select(Config))
            config = result.scalars().first()

            if not config:
                logger.info("Creating default configuration")
                default_config = Config(
                    qwen_api_key="",
                    default_language="en",
                    post_frequency=1,  # hours
                    symbols="BTC/USD,ETH/USD,EUR/USD,GBP/USD",
                    timeframes="1h,4h,1d",
                    enable_news=True,
                    enable_signals=True,
                    enable_calendar=True,
                    max_tokens_per_request=4000
                )
                session.add(default_config)
                session.commit()
        else:
            result = await session.execute(select(Config))
            config = result.scalars().first()

            if not config:
                logger.info("Creating default configuration")
                default_config = Config(
                    qwen_api_key="",
                    default_language="en",
                    post_frequency=1,  # hours
                    symbols="BTC/USD,ETH/USD,EUR/USD,GBP/USD",
                    timeframes="1h,4h,1d",
                    enable_news=True,
                    enable_signals=True,
                    enable_calendar=True,
                    max_tokens_per_request=4000
                )
                session.add(default_config)
                await session.commit()

def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def get_async_db():
    """Get async database session"""
    # For SQLite, we'll use synchronous operations wrapped in async
    if AsyncSessionLocal is None:
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()
    else:
        async with AsyncSessionLocal() as session:
            yield session

# Flag to check if we're using SQLite
def is_sqlite_db():
    """Check if we're using SQLite database"""
    return DATABASE_URL.startswith("sqlite:")
