"""
AI-based signal generator using prompt-based strategies
"""

import logging
import json
from datetime import datetime, timezone
from sqlalchemy import select

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import Strategy, TradingSignal, CandleData, EconomicEvent
from src.ai_integration.qwen_client import <PERSON>wen<PERSON>lient
from src.data_collection.market_data import get_mt5_candle_data

logger = logging.getLogger(__name__)

class AISignalGenerator:
    """AI-based signal generator using prompt strategies"""
    
    def __init__(self):
        self.qwen_client = QwenClient()
        
    async def generate_signals_for_strategy(self, strategy):
        """
        Generate trading signals for a specific strategy using AI

        Args:
            strategy (Strategy): Strategy object with prompt

        Returns:
            list: List of generated signals
        """
        from datetime import datetime, timezone

        signals = []
        strategy_start = datetime.now(timezone.utc)

        try:
            logger.info(f"🤖 Starting AI signal generation for strategy: {strategy.name} at {strategy_start.strftime('%H:%M:%S UTC')}")
            logger.info(f"📊 Strategy ID: {strategy.id}, Active: {strategy.active}")

            # Parse symbols and timeframes
            symbols = [s.strip() for s in strategy.symbols.split(',')]
            timeframes = [t.strip() for t in strategy.timeframes.split(',')]

            logger.info(f"🎯 Target symbols: {symbols}")
            logger.info(f"⏰ Target timeframes: {timeframes}")
            logger.info(f"📝 Strategy prompt length: {len(strategy.strategy_prompt) if strategy.strategy_prompt else 0} characters")

            for symbol in symbols:
                for timeframe in timeframes:
                    try:
                        logger.info(f"🔍 Processing {symbol} {timeframe}...")

                        # Get market data
                        market_data = await self.get_market_data(symbol, timeframe)
                        if not market_data:
                            logger.warning(f"❌ No market data for {symbol} {timeframe}")
                            continue

                        logger.info(f"📈 Retrieved {len(market_data)} candles for {symbol} {timeframe}")
                        logger.info(f"📊 Latest candle: Open={market_data[-1]['open']}, Close={market_data[-1]['close']}, High={market_data[-1]['high']}, Low={market_data[-1]['low']}")

                        # Get current events and news context
                        context = await self.get_market_context()
                        logger.info(f"🌍 Market context: {len(context.get('events', []))} events, {len(context.get('news', []))} news items")

                        # Generate signal using AI
                        signal = await self.generate_ai_signal(
                            strategy, symbol, timeframe, market_data, context
                        )

                        if signal:
                            signals.append(signal)
                            logger.info(f"✅ Generated signal for {symbol} {timeframe}: {signal['direction']} at {signal['entry_price']}")
                        else:
                            logger.info(f"❌ No signal generated for {symbol} {timeframe}")

                    except Exception as e:
                        logger.error(f"💥 Error generating signal for {symbol} {timeframe}: {e}")
                        continue

        except Exception as e:
            logger.error(f"💥 Error in AI signal generation for strategy {strategy.name}: {e}")

        strategy_duration = (datetime.now(timezone.utc) - strategy_start).total_seconds()
        logger.info(f"🎯 Total signals generated for strategy {strategy.name}: {len(signals)} in {strategy_duration:.2f}s")
        return signals
        
    async def get_market_data(self, symbol, timeframe, count=50):
        """
        Get recent market data for analysis
        
        Args:
            symbol (str): Trading symbol
            timeframe (str): Timeframe
            count (int): Number of candles to fetch
            
        Returns:
            list: Recent candle data
        """
        try:
            # First try to get from database
            async for db in get_async_db():
                try:
                    if is_sqlite_db():
                        result = db.execute(
                            select(CandleData)
                            .where(
                                CandleData.symbol == symbol,
                                CandleData.timeframe == timeframe
                            )
                            .order_by(CandleData.timestamp.desc())
                            .limit(count)
                        )
                    else:
                        result = await db.execute(
                            select(CandleData)
                            .where(
                                CandleData.symbol == symbol,
                                CandleData.timeframe == timeframe
                            )
                            .order_by(CandleData.timestamp.desc())
                            .limit(count)
                        )
                    
                    candles = result.scalars().all()
                    
                    if candles and len(candles) >= 10:  # Need at least 10 candles
                        # Convert to list format (newest first, so reverse)
                        candle_data = []
                        for candle in reversed(candles):
                            candle_data.append({
                                "timestamp": candle.timestamp,
                                "open": candle.open,
                                "high": candle.high,
                                "low": candle.low,
                                "close": candle.close,
                                "volume": candle.volume
                            })
                        return candle_data
                    else:
                        # Not enough data in database, fetch from MT5
                        logger.info(f"Fetching fresh data for {symbol} {timeframe}")
                        mt5_symbol = symbol.replace("/", "").replace("-", "")
                        fresh_data = await get_mt5_candle_data(mt5_symbol, timeframe, count)
                        return fresh_data
                        
                except Exception as e:
                    logger.error(f"Error getting market data: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Error in get_market_data: {e}")
            
        return []
        
    async def get_market_context(self):
        """
        Get current market context (news, events, etc.)
        
        Returns:
            dict: Market context information
        """
        context = {
            "events": [],
            "news": [],
            "market_sentiment": "neutral",
            "current_time": datetime.now(timezone.utc).isoformat()
        }
        
        try:
            # Get today's economic events
            today = datetime.now(timezone.utc).date()
            
            async for db in get_async_db():
                try:
                    if is_sqlite_db():
                        result = db.execute(
                            select(EconomicEvent)
                            .where(EconomicEvent.event_time >= today)
                            .order_by(EconomicEvent.impact.desc())
                            .limit(10)
                        )
                    else:
                        result = await db.execute(
                            select(EconomicEvent)
                            .where(EconomicEvent.event_time >= today)
                            .order_by(EconomicEvent.impact.desc())
                            .limit(10)
                        )
                    
                    events = result.scalars().all()
                    
                    for event in events:
                        context["events"].append({
                            "title": event.title,
                            "country": event.country,
                            "currency": event.currency,
                            "impact": event.impact,
                            "time": event.event_time.isoformat(),
                            "forecast": event.forecast,
                            "previous": event.previous,
                            "actual": event.actual
                        })
                    
                    break
                    
                except Exception as e:
                    logger.error(f"Error getting events context: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Error in get_market_context: {e}")
            
        return context
        
    async def generate_ai_signal(self, strategy, symbol, timeframe, market_data, context):
        """
        Generate trading signal using AI and strategy prompt
        
        Args:
            strategy (Strategy): Strategy with prompt
            symbol (str): Trading symbol
            timeframe (str): Timeframe
            market_data (list): Recent candle data
            context (dict): Market context
            
        Returns:
            dict: Generated signal or None
        """
        try:
            # Prepare market data summary for AI
            if not market_data or len(market_data) < 5:
                return None
                
            latest_candle = market_data[-1]
            current_price = latest_candle["close"]
            
            # Create market data summary
            # Safely calculate price change
            price_change_24h = 0
            if len(market_data) >= 24 and market_data[-24].get("close"):
                try:
                    old_price = market_data[-24]["close"]
                    if old_price and old_price != 0:
                        price_change_24h = ((current_price - old_price) / old_price * 100)
                except (KeyError, TypeError, ZeroDivisionError):
                    price_change_24h = 0

            data_summary = {
                "symbol": symbol,
                "timeframe": timeframe,
                "current_price": current_price or 0,
                "latest_candles": market_data[-10:],  # Last 10 candles
                "price_change_24h": price_change_24h,
                "volume_trend": "increasing" if (latest_candle.get("volume") and market_data[-2].get("volume") and latest_candle["volume"] > market_data[-2]["volume"]) else "decreasing"
            }

            # Create AI prompt
            prompt = f"""
{strategy.strategy_prompt}

CURRENT MARKET DATA:
Symbol: {symbol}
Timeframe: {timeframe}
Current Price: {current_price or 0}
24h Change: {price_change_24h:.2f}%
Volume Trend: {data_summary['volume_trend']}

RECENT CANDLES (Last 5):
"""
            
            # Add recent candle data
            for i, candle in enumerate(market_data[-5:]):
                # Safely format candle data, handling None values
                open_price = candle.get('open', 0) or 0
                high_price = candle.get('high', 0) or 0
                low_price = candle.get('low', 0) or 0
                close_price = candle.get('close', 0) or 0
                volume = candle.get('volume', 0) or 0

                prompt += f"Candle {i+1}: O:{open_price:.5f} H:{high_price:.5f} L:{low_price:.5f} C:{close_price:.5f} V:{volume:.0f}\n"
            
            # Add market context
            if context["events"]:
                prompt += f"\nUPCOMING EVENTS:\n"
                for event in context["events"][:3]:  # Top 3 events
                    prompt += f"- {event['title']} ({event['country']}) - Impact: {event['impact']}\n"
            
            prompt += f"\nCurrent Time: {context['current_time']}\n"
            prompt += f"\nStrategy Parameters: {json.dumps(strategy.parameters, indent=2)}\n"
            prompt += f"""
STRATEGY PROMPT:
{strategy.strategy_prompt}

IMPORTANT: You MUST respond with ONLY valid JSON in this exact format:

If you find a trading signal:
{{"signal": true, "direction": "buy", "entry_price": 1.2345, "stop_loss": 1.2300, "take_profit_1": 1.2400, "take_profit_2": 1.2450, "take_profit_3": 1.2500, "confidence": 0.8, "reasoning": "Clear bullish breakout above resistance"}}

If no signal:
{{"signal": false, "reasoning": "No clear setup found"}}

REQUIREMENTS:
- take_profit_1 is REQUIRED (closest target)
- take_profit_2 is OPTIONAL (medium target)
- take_profit_3 is OPTIONAL (furthest target)
- If only one TP, use take_profit_1 only
- If two TPs, use take_profit_1 and take_profit_2
- If three TPs, use all three
- TPs must be in ascending order for BUY, descending for SELL

Do NOT include any other text, explanations, or formatting. Only return the JSON object."""

            logger.info(f"🤖 Sending AI prompt for {symbol} {timeframe}:")
            logger.info(f"📝 Prompt length: {len(prompt)} characters")
            logger.info(f"🎯 Strategy prompt: {strategy.strategy_prompt[:200]}..." if len(strategy.strategy_prompt) > 200 else f"🎯 Strategy prompt: {strategy.strategy_prompt}")
            logger.info(f"📊 Market data points: {len(market_data)}")
            logger.info(f"🌍 Context events: {len(context.get('events', []))}")
            logger.info(f"📰 Context news: {len(context.get('news', []))}")

            # Get AI response with timing
            ai_start = datetime.now(timezone.utc)
            response = await self.qwen_client.generate_content(prompt, max_tokens=500)
            ai_duration = (datetime.now(timezone.utc) - ai_start).total_seconds()

            if not response:
                logger.warning(f"❌ No response from AI for {symbol} {timeframe} (took {ai_duration:.2f}s)")
                return None

            logger.info(f"🤖 AI response received for {symbol} {timeframe} in {ai_duration:.2f}s")

            logger.info(f"🤖 AI Response for {symbol} {timeframe}: {response[:500]}..." if len(response) > 500 else f"🤖 AI Response for {symbol} {timeframe}: {response}")

            # Clean and try to parse JSON response
            try:
                logger.info(f"🔧 Processing AI response for {symbol} {timeframe}...")

                # Clean the response - remove any markdown formatting or extra text
                cleaned_response = response.strip()
                logger.info(f"📝 Original response length: {len(response)}")

                if cleaned_response.startswith("```json"):
                    cleaned_response = cleaned_response.replace("```json", "").replace("```", "").strip()
                    logger.info("🧹 Removed JSON markdown formatting")
                elif cleaned_response.startswith("```"):
                    cleaned_response = cleaned_response.replace("```", "").strip()
                    logger.info("🧹 Removed markdown formatting")

                # Find JSON object in response
                start_idx = cleaned_response.find('{')
                end_idx = cleaned_response.rfind('}')
                logger.info(f"🔍 JSON boundaries: start={start_idx}, end={end_idx}")

                if start_idx != -1 and end_idx != -1:
                    json_str = cleaned_response[start_idx:end_idx+1]
                    logger.info(f"📋 Extracted JSON: {json_str}")
                    signal_data = json.loads(json_str)
                else:
                    # Try parsing the whole cleaned response
                    logger.info(f"📋 Parsing full response as JSON: {cleaned_response}")
                    signal_data = json.loads(cleaned_response)

                logger.info(f"✅ Successfully parsed JSON: {signal_data}")

                if signal_data.get("signal") == True:
                    logger.info(f"🎯 Signal detected! Direction: {signal_data.get('direction')}, Entry: {signal_data.get('entry_price')}")

                    # Handle multi-TP levels
                    take_profit_1 = signal_data.get("take_profit_1") or signal_data.get("take_profit")
                    take_profit_2 = signal_data.get("take_profit_2")
                    take_profit_3 = signal_data.get("take_profit_3")

                    if not take_profit_1:
                        logger.error(f"❌ No take_profit_1 or take_profit found in signal data")
                        return None

                    # Create signal record
                    signal = {
                        "strategy_id": strategy.id,
                        "symbol": symbol,
                        "timeframe": timeframe,
                        "direction": signal_data["direction"],
                        "entry_price": float(signal_data["entry_price"]),
                        "stop_loss": float(signal_data["stop_loss"]),
                        "take_profit_1": float(take_profit_1),
                        "take_profit_2": float(take_profit_2) if take_profit_2 else None,
                        "take_profit_3": float(take_profit_3) if take_profit_3 else None,
                        "take_profit": float(take_profit_1),  # Legacy field for backward compatibility
                        "confidence": float(signal_data.get("confidence", 0.7)),
                        "reasoning": signal_data.get("reasoning", "AI generated signal"),
                        "signal_time": datetime.now(timezone.utc),
                        "status": "active"
                    }

                    logger.info(f"✅ Created signal object for {symbol} {timeframe}: {signal['direction']} at {signal['entry_price']}")
                    tp_info = f"TP1={signal['take_profit_1']}"
                    if signal['take_profit_2']:
                        tp_info += f", TP2={signal['take_profit_2']}"
                    if signal['take_profit_3']:
                        tp_info += f", TP3={signal['take_profit_3']}"
                    logger.info(f"📊 Signal details: SL={signal['stop_loss']}, {tp_info}, Confidence={signal['confidence']}")
                    return signal
                else:
                    logger.info(f"❌ No signal found in AI response for {symbol} {timeframe}: {signal_data.get('reasoning', 'No reason provided')}")

            except (json.JSONDecodeError, KeyError, ValueError) as e:
                logger.error(f"💥 Could not parse AI response as signal for {symbol} {timeframe}: {e}")
                logger.info(f"🔍 Raw AI response: {response}")
                logger.info(f"🔍 Cleaned response: {cleaned_response}")
                
        except Exception as e:
            logger.error(f"Error generating AI signal: {e}")
            
        return None

# Global instance
ai_signal_generator = AISignalGenerator()
