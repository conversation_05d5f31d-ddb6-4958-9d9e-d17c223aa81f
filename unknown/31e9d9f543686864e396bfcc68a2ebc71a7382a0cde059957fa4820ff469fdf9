"""
Enhanced logging setup for MignalyBot v2

Features:
- Performance logging
- Structured logging
- Log rotation
- Multiple handlers
- Custom formatters
"""

import logging
import logging.handlers
import sys
import time
from typing import Optional
from pathlib import Path

from core.config.settings import Settings


class PerformanceFilter(logging.Filter):
    """Filter for performance-related log records"""
    
    def filter(self, record):
        return hasattr(record, 'performance') and record.performance


class StructuredFormatter(logging.Formatter):
    """Structured formatter that adds context information"""
    
    def format(self, record):
        # Add timestamp
        record.timestamp = time.time()
        
        # Add performance metrics if available
        if hasattr(record, 'duration'):
            record.performance_ms = f"{record.duration * 1000:.2f}ms"
        
        # Add memory usage if available
        if hasattr(record, 'memory_mb'):
            record.memory = f"{record.memory_mb:.2f}MB"
        
        return super().format(record)


class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def log_duration(self, operation: str, duration: float, **kwargs):
        """Log operation duration"""
        extra = {
            'performance': True,
            'operation': operation,
            'duration': duration,
            **kwargs
        }
        self.logger.info(f"Performance: {operation} took {duration*1000:.2f}ms", extra=extra)
    
    def log_memory_usage(self, operation: str, memory_mb: float, **kwargs):
        """Log memory usage"""
        extra = {
            'performance': True,
            'operation': operation,
            'memory_mb': memory_mb,
            **kwargs
        }
        self.logger.info(f"Memory: {operation} used {memory_mb:.2f}MB", extra=extra)


def setup_logging(settings: Settings) -> logging.Logger:
    """
    Setup enhanced logging system
    
    Args:
        settings: Application settings
        
    Returns:
        Configured logger instance
    """
    # Create root logger
    logger = logging.getLogger("mignalybot_v2")
    logger.setLevel(getattr(logging, settings.logging.level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = StructuredFormatter(settings.logging.format)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    logger.addHandler(console_handler)
    
    # File handler (if configured)
    if settings.logging.file_path:
        file_path = Path(settings.logging.file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=settings.logging.max_file_size,
            backupCount=settings.logging.backup_count
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(getattr(logging, settings.logging.level.upper()))
        logger.addHandler(file_handler)
    
    # Performance handler (if enabled)
    if settings.logging.enable_performance_logs:
        perf_handler = logging.StreamHandler(sys.stdout)
        perf_formatter = StructuredFormatter(
            "%(asctime)s - PERFORMANCE - %(operation)s - %(message)s"
        )
        perf_handler.setFormatter(perf_formatter)
        perf_handler.addFilter(PerformanceFilter())
        logger.addHandler(perf_handler)
    
    # Set logging levels for third-party libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("telegram").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    
    # Enable SQLAlchemy logging in debug mode
    if settings.debug:
        logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)
    
    logger.info("Logging system initialized")
    return logger


def get_performance_logger(logger_name: str = "mignalybot_v2") -> PerformanceLogger:
    """Get performance logger instance"""
    logger = logging.getLogger(logger_name)
    return PerformanceLogger(logger)


class LoggingContext:
    """Context manager for logging with performance tracking"""
    
    def __init__(self, logger: logging.Logger, operation: str, level: int = logging.INFO):
        self.logger = logger
        self.operation = operation
        self.level = level
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.log(self.level, f"Starting {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            extra = {
                'performance': True,
                'operation': self.operation,
                'duration': duration
            }
            self.logger.log(
                self.level,
                f"Completed {self.operation} in {duration*1000:.2f}ms",
                extra=extra
            )
        else:
            self.logger.error(
                f"Failed {self.operation} after {duration*1000:.2f}ms: {exc_val}",
                exc_info=True
            )


def log_performance(operation: str, level: int = logging.INFO):
    """Decorator for logging function performance"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = logging.getLogger("mignalybot_v2")
            with LoggingContext(logger, f"{func.__name__}:{operation}", level):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def log_async_performance(operation: str, level: int = logging.INFO):
    """Decorator for logging async function performance"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            logger = logging.getLogger("mignalybot_v2")
            with LoggingContext(logger, f"{func.__name__}:{operation}", level):
                return await func(*args, **kwargs)
        return wrapper
    return decorator
