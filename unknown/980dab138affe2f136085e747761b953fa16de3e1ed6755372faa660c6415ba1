"""
Base database models for MignalyBot v2

Optimized models with:
- Proper indexing
- Efficient relationships
- Timezone handling
- Performance optimizations
"""

from datetime import datetime
from typing import Any, Dict
from sqlalchemy import Column, Integer, DateTime, Index
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.orm import Session
import pytz


class BaseModel:
    """Base model with common fields and methods"""
    
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower() + 's'
    
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.UTC), index=True)
    updated_at = Column(
        DateTime(timezone=True),
        default=lambda: datetime.now(pytz.UTC),
        onupdate=lambda: datetime.now(pytz.UTC),
        index=True
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result
    
    def update_from_dict(self, data: Dict[str, Any]):
        """Update model from dictionary"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    @classmethod
    def create(cls, session: Session, **kwargs):
        """Create new instance"""
        instance = cls(**kwargs)
        session.add(instance)
        return instance
    
    def save(self, session: Session):
        """Save instance to database"""
        session.add(self)
        return self
    
    def delete(self, session: Session):
        """Delete instance from database"""
        session.delete(self)


# Create base class
Base = declarative_base(cls=BaseModel)


# Common indexes for performance
def create_common_indexes():
    """Create common indexes for better query performance"""
    indexes = [
        # Time-based queries
        Index('idx_created_at', BaseModel.created_at),
        Index('idx_updated_at', BaseModel.updated_at),
    ]
    return indexes
