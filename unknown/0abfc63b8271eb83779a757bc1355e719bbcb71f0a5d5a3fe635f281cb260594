"""
Event Result Processor for MignalyBot
Handles checking for event results and generating result messages
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import List
from sqlalchemy import select, and_

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import (
    EconomicEvent, Channel, Post, PostType, PostStatus
)
from src.ai_integration.qwen_client import QwenClient
from src.utils.helpers import get_current_time, get_timezone

logger = logging.getLogger(__name__)

class EventResultProcessor:
    """Processes economic events and generates result messages"""
    
    def __init__(self):
        self.qwen_client = QwenClient()
    
    async def check_and_process_event_results(self):
        """Check for events that have occurred and generate result messages"""
        logger.info("Checking for event results to process...")
        
        try:
            # Get events that occurred in the last 2 hours and have actual results
            events = await self.get_events_with_results()
            
            if not events:
                logger.info("No events with results found")
                return
            
            logger.info(f"Found {len(events)} events with results to process")
            
            # Get active channels
            channels = await self.get_active_channels()
            
            if not channels:
                logger.warning("No active channels found")
                return
            
            # Process each event
            for event in events:
                await self.process_event_result(event, channels)
                
        except Exception as e:
            logger.error(f"Error checking event results: {e}", exc_info=True)
    
    async def get_events_with_results(self) -> List[EconomicEvent]:
        """Get events that have occurred and have actual results"""
        events = []
        
        async for db in get_async_db():
            try:
                now = get_current_time()
                # Look for events that occurred in the last 2 hours
                start_time = now - timedelta(hours=2)

                # Ensure timezone-aware datetimes for database comparison
                # Since event_time is now stored as timezone-aware (UTC), we need timezone-aware datetimes
                if now.tzinfo is None:
                    now_utc = now.replace(tzinfo=timezone.utc)
                else:
                    now_utc = now.astimezone(timezone.utc)

                if start_time.tzinfo is None:
                    start_time_utc = start_time.replace(tzinfo=timezone.utc)
                else:
                    start_time_utc = start_time.astimezone(timezone.utc)
                
                if is_sqlite_db():
                    result = db.execute(
                        select(EconomicEvent)
                        .where(
                            and_(
                                EconomicEvent.event_time >= start_time_utc,
                                EconomicEvent.event_time <= now_utc,
                                EconomicEvent.actual.is_not(None),  # Has actual results
                                EconomicEvent.actual != "",  # Not empty
                                EconomicEvent.impact >= 2  # Medium or high impact only
                            )
                        )
                        .order_by(EconomicEvent.event_time.desc())
                    )
                else:
                    result = await db.execute(
                        select(EconomicEvent)
                        .where(
                            and_(
                                EconomicEvent.event_time >= start_time_utc,
                                EconomicEvent.event_time <= now_utc,
                                EconomicEvent.actual.is_not(None),  # Has actual results
                                EconomicEvent.actual != "",  # Not empty
                                EconomicEvent.impact >= 2  # Medium or high impact only
                            )
                        )
                        .order_by(EconomicEvent.event_time.desc())
                    )
                
                events = result.scalars().all()
                break
                
            except Exception as e:
                logger.error(f"Error getting events with results: {e}")
                break
        
        return events
    
    async def get_active_channels(self) -> List[Channel]:
        """Get all active channels"""
        channels = []
        
        async for db in get_async_db():
            try:
                if is_sqlite_db():
                    result = db.execute(
                        select(Channel).where(Channel.active == True)
                    )
                else:
                    result = await db.execute(
                        select(Channel).where(Channel.active == True)
                    )
                
                channels = result.scalars().all()
                break
                
            except Exception as e:
                logger.error(f"Error getting active channels: {e}")
                break
        
        return channels
    
    async def process_event_result(self, event: EconomicEvent, channels: List[Channel]):
        """Process a single event result for all channels"""
        logger.info(f"Processing result for event: {event.title}")
        
        for channel in channels:
            try:
                # Check if we already created a result post for this event and channel
                if await self.result_post_already_exists(event, channel):
                    logger.info(f"Result post already exists for event {event.title} in channel {channel.name}")
                    continue
                
                # Check if this channel has event posts enabled
                post_types = channel.post_types.split(",")
                if "event" not in post_types:
                    logger.info(f"Event posts disabled for channel {channel.name}")
                    continue
                
                # Generate result message
                result_content = await self.generate_event_result_message(event, channel)
                
                if not result_content:
                    logger.warning(f"Failed to generate result content for event {event.title}")
                    continue
                
                # Create and schedule the result post
                await self.create_result_post(event, channel, result_content)
                
                logger.info(f"Created result post for event {event.title} in channel {channel.name}")
                
            except Exception as e:
                logger.error(f"Error processing event result for channel {channel.name}: {e}")
    
    async def result_post_already_exists(self, event: EconomicEvent, channel: Channel) -> bool:
        """Check if a result post already exists for this event and channel"""
        async for db in get_async_db():
            try:
                if is_sqlite_db():
                    result = db.execute(
                        select(Post)
                        .where(
                            and_(
                                Post.channel_id == channel.id,
                                Post.event_id == event.id,
                                Post.type == PostType.EVENT,
                                Post.content.contains("[result]")  # Mark result posts
                            )
                        )
                    )
                else:
                    result = await db.execute(
                        select(Post)
                        .where(
                            and_(
                                Post.channel_id == channel.id,
                                Post.event_id == event.id,
                                Post.type == PostType.EVENT,
                                Post.content.contains("[result]")  # Mark result posts
                            )
                        )
                    )
                
                existing_post = result.scalars().first()
                return existing_post is not None
                
            except Exception as e:
                logger.error(f"Error checking existing result post: {e}")
                return False
    
    async def generate_event_result_message(self, event: EconomicEvent, channel: Channel) -> str:
        """Generate AI result message for an event"""
        try:
            # Use the existing analyze_economic_event method but modify the content to focus on results
            result_content = await self.qwen_client.analyze_economic_event(
                event,
                language=channel.language,
                channel_brand=channel.brand_name
            )
            
            # Mark this as a result post
            result_content = f"[result] {result_content}"
            
            return result_content
            
        except Exception as e:
            logger.error(f"Error generating event result message: {e}")
            return None
    
    async def create_result_post(self, event: EconomicEvent, channel: Channel, content: str):
        """Create a result post for the event"""
        async for db in get_async_db():
            try:
                # Use UTC time for database storage
                now_utc = get_utc_time()

                # Schedule the post to be sent immediately (within 1-5 minutes)
                import random
                scheduled_time_utc = now_utc + timedelta(minutes=random.randint(1, 5))

                post = Post(
                    channel_id=channel.id,
                    type=PostType.EVENT,
                    content=content,
                    status=PostStatus.SCHEDULED,
                    scheduled_time=scheduled_time_utc,
                    event_id=event.id,
                    created_at=now_utc
                )
                
                db.add(post)
                
                if is_sqlite_db():
                    db.commit()
                else:
                    await db.commit()
                    
                logger.info(f"Created result post scheduled for {scheduled_time_utc}")
                
            except Exception as e:
                logger.error(f"Error creating result post: {e}")
                if is_sqlite_db():
                    db.rollback()
                else:
                    await db.rollback()

# Main function to be called by scheduler
async def process_event_results():
    """Main function to process event results"""
    processor = EventResultProcessor()
    await processor.check_and_process_event_results()
