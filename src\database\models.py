"""
Database models for MignalyBot
"""

from datetime import datetime
from enum import Enum as PyEnum
import pytz
from sqlalchemy import (
    Column, Integer, String, Float, Boolean,
    DateTime, Text, ForeignKey, Enum, JSON, UniqueConstraint
)
from sqlalchemy.orm import relationship
from src.database.setup import Base

# Helper function for consistent timezone handling
def get_current_utc_time():
    """Get current UTC time for database storage"""
    return datetime.now(pytz.UTC)

def ensure_utc_datetime(dt):
    """
    Ensure a datetime is in UTC for database storage

    Args:
        dt: datetime object or None

    Returns:
        datetime in UTC or None
    """
    if dt is None:
        return None

    if dt.tzinfo is None:
        # Assume naive datetime is already in UTC
        return pytz.UTC.localize(dt)

    return dt.astimezone(pytz.UTC)

class SignalStatus(PyEnum):
    """Trading signal status"""
    ACTIVE = "active"
    TP1_HIT = "tp1_hit"
    TP2_HIT = "tp2_hit"
    TP3_HIT = "tp3_hit"
    ALL_TP_HIT = "all_tp_hit"
    SL_HIT = "sl_hit"
    BREAK_EVEN = "break_even"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    # Legacy status for backward compatibility
    TP_HIT = "tp_hit"

class PostStatus(PyEnum):
    """Post status"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    PUBLISHED = "published"
    FAILED = "failed"

class PostType(PyEnum):
    """Post type"""
    NEWS = "news"
    SIGNAL = "signal"
    ANALYSIS = "analysis"
    EVENT = "event"
    PERFORMANCE = "performance"
    GREETING = "greeting"

class Config(Base):
    """Application configuration"""
    __tablename__ = "config"

    id = Column(Integer, primary_key=True)
    qwen_api_key = Column(String(255), nullable=True)
    default_language = Column(String(10), default="en")
    post_frequency = Column(Integer, default=1)  # hours
    symbols = Column(String(255), default="BTC/USD,ETH/USD,EUR/USD,GBP/USD")
    timeframes = Column(String(255), default="1h,4h,1d")
    enable_news = Column(Boolean, default=True)
    enable_signals = Column(Boolean, default=True)
    enable_calendar = Column(Boolean, default=True)
    max_tokens_per_request = Column(Integer, default=4000)
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)
    updated_at = Column(DateTime(timezone=True), default=get_current_utc_time, onupdate=get_current_utc_time)

class Channel(Base):
    """Telegram channel configuration"""
    __tablename__ = "channels"

    id = Column(Integer, primary_key=True)
    chat_id = Column(String(255), nullable=False, unique=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    language = Column(String(10), default="en")
    active = Column(Boolean, default=True)
    brand_name = Column(String(255), nullable=True)
    brand_description = Column(Text, nullable=True)

    # Advertisement footer settings
    enable_advertisement = Column(Boolean, default=False)
    advertisement_text = Column(String(500), default="This message generated by Mignaly")
    advertisement_url = Column(String(255), default="https://mignaly.com")

    post_types = Column(String(255), default="news,signal,analysis,event,performance,greeting")

    # Date sticker settings
    enable_date_stickers = Column(Boolean, default=True)
    date_sticker_style = Column(String(50), default="modern")  # modern, minimal, classic

    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)
    updated_at = Column(DateTime(timezone=True), default=get_current_utc_time, onupdate=get_current_utc_time)

    # Relationships
    posts = relationship("Post", back_populates="channel")


class Strategy(Base):
    """Trading strategy with AI prompt-based generation"""
    __tablename__ = "strategies"

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    symbols = Column(String(255), nullable=False)
    timeframes = Column(String(255), nullable=False)
    parameters = Column(JSON, nullable=True)

    # AI prompt-based strategy instead of code
    strategy_prompt = Column(Text, nullable=False)
    strategy_type = Column(String(100), default="general")  # general, trend_following, scalping, etc.

    # Legacy code field (kept for backward compatibility, but not used)
    code = Column(Text, nullable=True)

    active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)
    updated_at = Column(DateTime(timezone=True), default=get_current_utc_time, onupdate=get_current_utc_time)

    # Relationships
    signals = relationship("TradingSignal", back_populates="strategy")


class PromptTemplate(Base):
    """Editable prompt templates for different post types"""
    __tablename__ = "prompt_templates"

    id = Column(Integer, primary_key=True)
    post_type = Column(String(50), nullable=False)  # news, events, signals, market_analysis, countdown, greeting, signal_update, event_result
    language = Column(String(10), nullable=False, default="fa")  # fa, en, ar, etc.
    template_content = Column(Text, nullable=False)
    description = Column(Text, nullable=True)

    # Metadata
    active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)
    updated_at = Column(DateTime(timezone=True), default=get_current_utc_time, onupdate=get_current_utc_time)

    # Ensure unique combination of post_type and language
    __table_args__ = (
        UniqueConstraint('post_type', 'language', name='uq_prompt_template_type_lang'),
    )

class CandleData(Base):
    """Market candle data"""
    __tablename__ = "candle_data"

    id = Column(Integer, primary_key=True)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(Float, nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)

    __table_args__ = (
        # Composite unique constraint
        {'sqlite_autoincrement': True},
    )

class NewsItem(Base):
    """Financial news item"""
    __tablename__ = "news_items"

    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)
    source = Column(String(255), nullable=True)
    url = Column(String(512), nullable=True)
    image_url = Column(String(512), nullable=True)
    published_at = Column(DateTime(timezone=True), nullable=False)
    symbols = Column(String(255), nullable=True)  # Comma-separated symbols related to the news
    sentiment = Column(Float, nullable=True)  # -1 to 1 scale
    ai_analysis = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)

    # Relationships
    posts = relationship("Post", back_populates="news_item")

class EconomicEvent(Base):
    """Economic calendar event"""
    __tablename__ = "economic_events"

    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    country = Column(String(50), nullable=True)
    impact = Column(Integer, nullable=True)  # 1-3 scale
    previous = Column(String(50), nullable=True)
    forecast = Column(String(50), nullable=True)
    actual = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    event_time = Column(DateTime(timezone=True), nullable=False)
    currency = Column(String(10), nullable=True)
    ai_analysis = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)

    # Relationships
    posts = relationship("Post", back_populates="economic_event")

class TradingSignal(Base):
    """Trading signal with multi-level take profit support"""
    __tablename__ = "trading_signals"

    id = Column(Integer, primary_key=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False)
    symbol = Column(String(20), nullable=False)
    timeframe = Column(String(10), nullable=False)
    direction = Column(String(10), nullable=False)  # "buy" or "sell"
    entry_price = Column(Float, nullable=False)
    stop_loss = Column(Float, nullable=False)

    # Multi-level take profit support
    take_profit_1 = Column(Float, nullable=False)  # Primary TP (required)
    take_profit_2 = Column(Float, nullable=True)   # Secondary TP (optional)
    take_profit_3 = Column(Float, nullable=True)   # Tertiary TP (optional)

    # Legacy field for backward compatibility
    take_profit = Column(Float, nullable=True)

    # Position management
    initial_position_size = Column(Float, default=100.0)  # Percentage of initial position
    remaining_position_size = Column(Float, default=100.0)  # Current remaining position
    break_even_triggered = Column(Boolean, default=False)

    risk_reward = Column(Float, nullable=True)
    status = Column(Enum(SignalStatus), default=SignalStatus.ACTIVE)
    entry_time = Column(DateTime(timezone=True), default=get_current_utc_time)
    exit_time = Column(DateTime(timezone=True), nullable=True)
    exit_price = Column(Float, nullable=True)
    profit_loss = Column(Float, nullable=True)
    notes = Column(Text, nullable=True)
    chart_image = Column(String(512), nullable=True)
    ai_analysis = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)
    updated_at = Column(DateTime(timezone=True), default=get_current_utc_time, onupdate=get_current_utc_time)

    # Relationships
    strategy = relationship("Strategy", back_populates="signals")
    posts = relationship("Post", back_populates="trading_signal")
    take_profit_hits = relationship("TakeProfitHit", back_populates="signal", cascade="all, delete-orphan")


class TakeProfitHit(Base):
    """Track individual take profit level hits"""
    __tablename__ = "take_profit_hits"

    id = Column(Integer, primary_key=True)
    signal_id = Column(Integer, ForeignKey("trading_signals.id"), nullable=False)
    tp_level = Column(Integer, nullable=False)  # 1, 2, or 3
    tp_price = Column(Float, nullable=False)
    hit_time = Column(DateTime(timezone=True), nullable=False)
    hit_price = Column(Float, nullable=False)
    position_closed_percentage = Column(Float, nullable=False)  # Percentage of position closed
    profit_loss_percentage = Column(Float, nullable=False)  # P&L for this TP level
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)

    # Relationships
    signal = relationship("TradingSignal", back_populates="take_profit_hits")

class Post(Base):
    """Telegram channel post"""
    __tablename__ = "posts"

    id = Column(Integer, primary_key=True)
    channel_id = Column(Integer, ForeignKey("channels.id"), nullable=False)
    type = Column(Enum(PostType), nullable=False)
    content = Column(Text, nullable=False)
    status = Column(Enum(PostStatus), default=PostStatus.DRAFT)
    scheduled_time = Column(DateTime(timezone=True), nullable=True)  # Store as timezone-aware
    published_time = Column(DateTime(timezone=True), nullable=True)  # Store as timezone-aware
    message_id = Column(String(50), nullable=True)  # Telegram message ID
    reply_to_message_id = Column(String(50), nullable=True)  # For replying to original signal posts
    tp_level = Column(Integer, nullable=True)  # Which TP level this performance update is for (1, 2, 3)
    news_id = Column(Integer, ForeignKey("news_items.id"), nullable=True)
    event_id = Column(Integer, ForeignKey("economic_events.id"), nullable=True)
    signal_id = Column(Integer, ForeignKey("trading_signals.id"), nullable=True)
    image_path = Column(String(512), nullable=True)  # Legacy field for backward compatibility
    image_url = Column(String(512), nullable=True)   # Direct URL to image for Telegram
    created_at = Column(DateTime(timezone=True), default=get_current_utc_time)
    updated_at = Column(DateTime(timezone=True), default=get_current_utc_time, onupdate=get_current_utc_time)

    # Relationships
    channel = relationship("Channel", back_populates="posts")
    news_item = relationship("NewsItem", back_populates="posts")
    economic_event = relationship("EconomicEvent", back_populates="posts")
    trading_signal = relationship("TradingSignal", back_populates="posts")

    # Table constraints to prevent duplicate posts
    __table_args__ = (
        # Prevent duplicate signal posts for the same channel and signal
        UniqueConstraint('channel_id', 'signal_id', 'type', name='uq_post_channel_signal_type'),
        # Prevent duplicate event posts for the same channel and event
        UniqueConstraint('channel_id', 'event_id', 'type', name='uq_post_channel_event_type'),
        # Prevent duplicate news posts for the same channel and news item
        UniqueConstraint('channel_id', 'news_id', 'type', name='uq_post_channel_news_type'),
    )
