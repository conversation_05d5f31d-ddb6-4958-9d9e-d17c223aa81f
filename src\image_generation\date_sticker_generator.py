"""
Date sticker generator for MignalyBot greeting messages
Creates Telegram-compatible date stickers showing today's date
"""

import logging
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime
import os
from typing import Dict, Any, Optional
import tempfile
import jdatetime  # For Persian calendar support

logger = logging.getLogger(__name__)

class DateStickerGenerator:
    """Generate date stickers for greeting messages"""

    def __init__(self):
        # Telegram sticker standard size
        self.width = 512
        self.height = 512

        # Detect Docker environment for enhanced compatibility
        self.docker_mode = os.environ.get('DOCKER_ENV', 'false').lower() == 'true' or os.path.exists('/.dockerenv')
        if self.docker_mode:
            logger.info("🐳 Docker environment detected for date sticker generator")

        # Darker, more beautiful color scheme
        self.bg_start = "#0D1117"    # Very dark gradient start
        self.bg_end = "#161B22"      # Dark gradient end
        self.text_primary = "#F0F6FC"  # Bright white text
        self.text_secondary = "#8B949E"  # Muted gray text
        self.accent_color = "#FFA500"    # Orange accent (more beautiful than yellow)
        self.border_color = "#30363D"    # Subtle dark border
        self.glow_color = "#FF6B35"      # Orange glow effect

        # Detect Docker environment for enhanced compatibility
        self.docker_mode = os.environ.get('DOCKER_ENV', 'false').lower() == 'true' or os.path.exists('/.dockerenv')
        if self.docker_mode:
            logger.info("🐳 Docker environment detected - using enhanced compatibility mode for date stickers")

        # Load fonts (fallback to default if not available)
        self.font_paths = {
            'regular': self._find_font(['arial.ttf', 'DejaVuSans.ttf', 'liberation-sans.ttf']),
            'bold': self._find_font(['arialbd.ttf', 'DejaVuSans-Bold.ttf', 'liberation-sans-bold.ttf']),
            'light': self._find_font(['arial.ttf', 'DejaVuSans.ttf', 'liberation-sans.ttf'])
        }

        # Persian number mapping
        self.persian_digits = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }

        # Persian month names
        self.persian_months = [
            'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
            'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'
        ]

        # English month names (short)
        self.english_months = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ]

    def _find_font(self, font_names):
        """Find available font file"""
        system_fonts = [
            '/System/Library/Fonts/',  # macOS
            '/usr/share/fonts/',       # Linux
            'C:/Windows/Fonts/',       # Windows
        ]
        
        for font_name in font_names:
            for font_dir in system_fonts:
                font_path = os.path.join(font_dir, font_name)
                if os.path.exists(font_path):
                    return font_path
        
        # Return None to use default font
        return None

    def _get_font(self, font_type='regular', size=24):
        """Get font object with enhanced fallback for Docker compatibility"""
        # Ensure minimum size for readability
        min_size = max(size, 16)

        # Try to load TrueType fonts first
        font_attempts = [
            # Try custom font path
            lambda: ImageFont.truetype(self.font_paths.get(font_type), min_size) if self.font_paths.get(font_type) else None,
            # Try common system fonts
            lambda: ImageFont.truetype("arial.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf", min_size),
            lambda: ImageFont.truetype("/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf", min_size),
        ]

        # Try TrueType fonts
        for i, attempt in enumerate(font_attempts):
            try:
                font = attempt()
                if font:
                    logger.debug(f"Loaded TrueType font {font_type} at size {min_size} (attempt {i+1})")
                    return font
            except Exception:
                continue

        # If no TrueType fonts available, create a larger default font
        logger.warning(f"No TrueType fonts available for {font_type}, using enhanced default font")

        # For Docker/environments without proper fonts, we need to simulate larger text
        try:
            default_font = ImageFont.load_default()
            # Store the requested size for later use in drawing
            default_font._requested_size = min_size
            return default_font
        except Exception as e:
            logger.error(f"Failed to load any font: {e}")
            return ImageFont.load_default()

    def _draw_text_enhanced(self, draw, position, text, fill, font):
        """Enhanced text drawing that handles default fonts better"""
        x, y = position

        # Check if this is a default font with a requested size
        if hasattr(font, '_requested_size'):
            requested_size = font._requested_size

            # For large requested sizes with default font, use multiple techniques
            if requested_size >= 100:
                # For very large text (like day numbers), draw multiple times with slight offsets for boldness
                offsets = [(0, 0), (1, 0), (0, 1), (1, 1)]
                for dx, dy in offsets:
                    draw.text((x + dx, y + dy), text, fill=fill, font=font)
            elif requested_size >= 40:
                # For medium text, draw twice for slight boldness
                draw.text((x, y), text, fill=fill, font=font)
                draw.text((x + 1, y), text, fill=fill, font=font)
            else:
                # For small text, draw normally
                draw.text((x, y), text, fill=fill, font=font)
        else:
            # Normal TrueType font, draw normally
            draw.text((x, y), text, fill=fill, font=font)

    def _create_gradient_background(self, draw, width, height):
        """Create gradient background similar to Binance PnL generator"""
        # Create vertical gradient from bg_start to bg_end
        for y in range(height):
            # Calculate gradient ratio
            ratio = y / height
            
            # Interpolate between start and end colors
            start_rgb = tuple(int(self.bg_start[i:i+2], 16) for i in (1, 3, 5))
            end_rgb = tuple(int(self.bg_end[i:i+2], 16) for i in (1, 3, 5))
            
            current_rgb = tuple(
                int(start_rgb[i] + (end_rgb[i] - start_rgb[i]) * ratio)
                for i in range(3)
            )
            
            color = f"#{current_rgb[0]:02x}{current_rgb[1]:02x}{current_rgb[2]:02x}"
            draw.line([(0, y), (width, y)], fill=color)

    def _to_persian_digits(self, text):
        """Convert English digits to Persian digits"""
        for eng, per in self.persian_digits.items():
            text = text.replace(eng, per)
        return text

    def _get_date_text(self, date_obj):
        """Get formatted date text - always use English format for consistency"""
        # Always use English/Gregorian calendar for all channels
        day = str(date_obj.day)
        month = self.english_months[date_obj.month - 1]
        year = str(date_obj.year)

        return {
            'day': day,
            'month': month,
            'year': year,
            'full': f"{day} {month} {year}"
        }

    def generate_date_sticker(self, date_obj=None):
        """
        Generate a beautiful date sticker image (always modern style, English format)

        Args:
            date_obj (datetime): Date to display (default: today)

        Returns:
            str: Path to generated sticker image file
        """
        try:
            if date_obj is None:
                from src.utils.helpers import get_current_time
                date_obj = get_current_time()

            logger.info(f"🎨 Generating beautiful date sticker for {date_obj.strftime('%Y-%m-%d')}")

            # Create image with RGBA for transparency support
            image = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(image)

            # Create beautiful dark background
            self._draw_beautiful_background(draw)

            # Get date text (always English format)
            date_info = self._get_date_text(date_obj)

            # Draw beautiful modern style
            self._draw_beautiful_modern_style(draw, date_info)

            # Save to temporary file
            temp_dir = tempfile.gettempdir()
            filename = f"date_sticker_{date_obj.strftime('%Y%m%d')}.png"
            file_path = os.path.join(temp_dir, filename)

            # Save with high quality
            image.save(file_path, 'PNG', optimize=True, quality=95)

            logger.info(f"✅ Beautiful date sticker generated successfully: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"❌ Error generating date sticker: {e}", exc_info=True)
            return None

    def _draw_beautiful_background(self, draw):
        """Draw beautiful dark background with subtle effects"""
        # Create smooth gradient background
        for y in range(self.height):
            ratio = y / self.height
            start_rgb = tuple(int(self.bg_start[i:i+2], 16) for i in (1, 3, 5))
            end_rgb = tuple(int(self.bg_end[i:i+2], 16) for i in (1, 3, 5))

            current_rgb = tuple(
                int(start_rgb[i] + (end_rgb[i] - start_rgb[i]) * ratio)
                for i in range(3)
            )

            color = current_rgb + (255,)  # Full opacity
            draw.line([(0, y), (self.width, y)], fill=color)

        # Add subtle border with rounded corners effect
        border_color = tuple(int(self.border_color[i:i+2], 16) for i in (1, 3, 5)) + (120,)

        # Draw rounded rectangle border
        corner_radius = 30
        for i in range(3):  # Multiple lines for thickness
            # Top and bottom lines
            draw.line([(corner_radius, i), (self.width - corner_radius, i)], fill=border_color, width=1)
            draw.line([(corner_radius, self.height - 1 - i), (self.width - corner_radius, self.height - 1 - i)], fill=border_color, width=1)

            # Left and right lines
            draw.line([(i, corner_radius), (i, self.height - corner_radius)], fill=border_color, width=1)
            draw.line([(self.width - 1 - i, corner_radius), (self.width - 1 - i, self.height - corner_radius)], fill=border_color, width=1)

        # Add corner accents for beauty
        accent_color = tuple(int(self.accent_color[i:i+2], 16) for i in (1, 3, 5)) + (80,)

        # Top-left accent
        draw.polygon([(20, 20), (50, 20), (20, 50)], fill=accent_color)

        # Bottom-right accent
        draw.polygon([(self.width - 20, self.height - 20),
                     (self.width - 50, self.height - 20),
                     (self.width - 20, self.height - 50)], fill=accent_color)

    def _draw_rounded_background(self, draw, style):
        """Draw rounded background with gradient"""
        # Create rounded rectangle background
        corner_radius = 40

        # Draw main background with gradient effect
        for y in range(self.height):
            ratio = y / self.height
            start_rgb = tuple(int(self.bg_start[i:i+2], 16) for i in (1, 3, 5))
            end_rgb = tuple(int(self.bg_end[i:i+2], 16) for i in (1, 3, 5))

            current_rgb = tuple(
                int(start_rgb[i] + (end_rgb[i] - start_rgb[i]) * ratio)
                for i in range(3)
            )

            color = current_rgb + (255,)  # Add alpha channel

            # Draw line with rounded edges consideration
            if corner_radius <= y <= self.height - corner_radius:
                draw.line([(0, y), (self.width, y)], fill=color)
            else:
                # Handle rounded corners
                if y < corner_radius:
                    # Top rounded corners
                    x_offset = int((corner_radius ** 2 - (corner_radius - y) ** 2) ** 0.5)
                    if x_offset > 0:
                        draw.line([(corner_radius - x_offset, y), (self.width - corner_radius + x_offset, y)], fill=color)
                else:
                    # Bottom rounded corners
                    y_from_bottom = self.height - y
                    x_offset = int((corner_radius ** 2 - (corner_radius - y_from_bottom) ** 2) ** 0.5)
                    if x_offset > 0:
                        draw.line([(corner_radius - x_offset, y), (self.width - corner_radius + x_offset, y)], fill=color)

        # Add subtle border
        border_width = 3
        border_color = tuple(int(self.border_color[i:i+2], 16) for i in (1, 3, 5)) + (180,)

        # Draw border (simplified rounded rectangle)
        for i in range(border_width):
            draw.rectangle([i, i, self.width-1-i, self.height-1-i], outline=border_color, width=1)

    def _draw_modern_style(self, draw, date_info, language):
        """Draw modern style date sticker"""
        center_x = self.width // 2
        center_y = self.height // 2

        # Large day number
        day_font = self._get_font('bold', 120)
        day_text = date_info['day']

        # Get text dimensions
        day_bbox = draw.textbbox((0, 0), day_text, font=day_font)
        day_width = day_bbox[2] - day_bbox[0]
        day_height = day_bbox[3] - day_bbox[1]

        # Draw day with accent color
        day_x = center_x - day_width // 2
        day_y = center_y - day_height // 2 - 30

        # Add glow effect for day
        glow_color = tuple(int(self.accent_color[i:i+2], 16) for i in (1, 3, 5)) + (100,)
        for offset in range(3, 0, -1):
            self._draw_text_enhanced(draw, (day_x + offset, day_y + offset), day_text, glow_color, day_font)

        self._draw_text_enhanced(draw, (day_x, day_y), day_text, self.accent_color, day_font)

        # Month text
        month_font = self._get_font('regular', 36)
        month_text = date_info['month']
        month_bbox = draw.textbbox((0, 0), month_text, font=month_font)
        month_width = month_bbox[2] - month_bbox[0]

        month_x = center_x - month_width // 2
        month_y = day_y + day_height + 10
        self._draw_text_enhanced(draw, (month_x, month_y), month_text, self.text_primary, month_font)

        # Year text
        year_font = self._get_font('light', 28)
        year_text = date_info['year']
        year_bbox = draw.textbbox((0, 0), year_text, font=year_font)
        year_width = year_bbox[2] - year_bbox[0]

        year_x = center_x - year_width // 2
        year_y = month_y + 50
        self._draw_text_enhanced(draw, (year_x, year_y), year_text, self.text_secondary, year_font)

        # Add decorative elements
        self._add_decorative_elements(draw, language)

    def _draw_beautiful_modern_style(self, draw, date_info):
        """Draw beautiful modern style date sticker"""
        center_x = self.width // 2
        center_y = self.height // 2

        # Large day number with beautiful glow effect
        day_font = self._get_font('bold', 140)
        day_text = date_info['day']

        # Get text dimensions
        day_bbox = draw.textbbox((0, 0), day_text, font=day_font)
        day_width = day_bbox[2] - day_bbox[0]
        day_height = day_bbox[3] - day_bbox[1]

        # Position day number
        day_x = center_x - day_width // 2
        day_y = center_y - day_height // 2 - 40

        # Create beautiful glow effect
        glow_color = tuple(int(self.glow_color[i:i+2], 16) for i in (1, 3, 5)) + (60,)
        for offset in range(8, 0, -1):
            alpha = int(60 * (8 - offset) / 8)
            glow_with_alpha = glow_color[:3] + (alpha,)
            self._draw_text_enhanced(draw, (day_x + offset//2, day_y + offset//2), day_text, glow_with_alpha, day_font)

        # Draw main day number with accent color
        self._draw_text_enhanced(draw, (day_x, day_y), day_text, self.accent_color, day_font)

        # Month text with elegant styling
        month_font = self._get_font('regular', 42)
        month_text = date_info['month'].upper()  # Uppercase for elegance
        month_bbox = draw.textbbox((0, 0), month_text, font=month_font)
        month_width = month_bbox[2] - month_bbox[0]

        month_x = center_x - month_width // 2
        month_y = day_y + day_height + 20

        # Add subtle shadow to month
        shadow_color = (0, 0, 0, 100)
        self._draw_text_enhanced(draw, (month_x + 2, month_y + 2), month_text, shadow_color, month_font)
        self._draw_text_enhanced(draw, (month_x, month_y), month_text, self.text_primary, month_font)

        # Year text with muted styling
        year_font = self._get_font('light', 32)
        year_text = date_info['year']
        year_bbox = draw.textbbox((0, 0), year_text, font=year_font)
        year_width = year_bbox[2] - year_bbox[0]

        year_x = center_x - year_width // 2
        year_y = month_y + 60
        self._draw_text_enhanced(draw, (year_x, year_y), year_text, self.text_secondary, year_font)

        # Add beautiful decorative line
        line_y = year_y + 40
        line_color = tuple(int(self.accent_color[i:i+2], 16) for i in (1, 3, 5)) + (150,)
        draw.line([(center_x - 60, line_y), (center_x + 60, line_y)], fill=line_color, width=3)

        # Add small dots for decoration
        dot_color = tuple(int(self.accent_color[i:i+2], 16) for i in (1, 3, 5)) + (200,)
        dot_radius = 4

        # Left dot
        draw.ellipse([center_x - 80 - dot_radius, line_y - dot_radius,
                     center_x - 80 + dot_radius, line_y + dot_radius], fill=dot_color)

        # Right dot
        draw.ellipse([center_x + 80 - dot_radius, line_y - dot_radius,
                     center_x + 80 + dot_radius, line_y + dot_radius], fill=dot_color)

    def _draw_minimal_style(self, draw, date_info, language):
        """Draw minimal style date sticker"""
        center_x = self.width // 2
        center_y = self.height // 2

        # Single line date format
        if language == "fa":
            date_text = date_info['full']
        else:
            date_text = f"{date_info['day']} {date_info['month']}"

        # Large, clean font
        main_font = self._get_font('light', 64)

        # Get text dimensions
        text_bbox = draw.textbbox((0, 0), date_text, font=main_font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]

        # Center the text
        text_x = center_x - text_width // 2
        text_y = center_y - text_height // 2

        # Draw text with subtle shadow
        shadow_color = (0, 0, 0, 80)
        self._draw_text_enhanced(draw, (text_x + 2, text_y + 2), date_text, shadow_color, main_font)
        self._draw_text_enhanced(draw, (text_x, text_y), date_text, self.text_primary, main_font)

        # Add year below if not already included
        if language != "fa":
            year_font = self._get_font('light', 24)
            year_bbox = draw.textbbox((0, 0), date_info['year'], font=year_font)
            year_width = year_bbox[2] - year_bbox[0]

            year_x = center_x - year_width // 2
            year_y = text_y + text_height + 20
            self._draw_text_enhanced(draw, (year_x, year_y), date_info['year'], self.text_secondary, year_font)

    def _draw_classic_style(self, draw, date_info, language):
        """Draw classic style date sticker"""
        center_x = self.width // 2

        # Calendar-like layout
        # Header with month/year
        header_font = self._get_font('bold', 32)
        if language == "fa":
            header_text = f"{date_info['month']} {date_info['year']}"
        else:
            header_text = f"{date_info['month']} {date_info['year']}"

        header_bbox = draw.textbbox((0, 0), header_text, font=header_font)
        header_width = header_bbox[2] - header_bbox[0]
        header_x = center_x - header_width // 2
        header_y = 80

        # Draw header background
        header_bg_color = tuple(int(self.accent_color[i:i+2], 16) for i in (1, 3, 5)) + (200,)
        draw.rectangle([50, header_y - 10, self.width - 50, header_y + 50], fill=header_bg_color)

        self._draw_text_enhanced(draw, (header_x, header_y), header_text, self.bg_start, header_font)

        # Large day number in center
        day_font = self._get_font('bold', 140)
        day_text = date_info['day']

        day_bbox = draw.textbbox((0, 0), day_text, font=day_font)
        day_width = day_bbox[2] - day_bbox[0]
        day_height = day_bbox[3] - day_bbox[1]

        day_x = center_x - day_width // 2
        day_y = 200

        # Draw day with border
        self._draw_text_enhanced(draw, (day_x, day_y), day_text, self.text_primary, day_font)

        # Add calendar grid lines for decoration
        grid_color = tuple(int(self.text_secondary[i:i+2], 16) for i in (1, 3, 5)) + (100,)

        # Horizontal lines
        for i in range(3):
            y = 380 + i * 30
            draw.line([80, y, self.width - 80, y], fill=grid_color, width=1)

        # Vertical lines
        for i in range(6):
            x = 80 + i * (self.width - 160) // 5
            draw.line([x, 360, x, 470], fill=grid_color, width=1)

    def _add_decorative_elements(self, draw, language):
        """Add decorative elements to the sticker"""
        # Add corner accents
        accent_color = tuple(int(self.accent_color[i:i+2], 16) for i in (1, 3, 5)) + (150,)

        # Top-left corner
        draw.polygon([(20, 20), (60, 20), (20, 60)], fill=accent_color)

        # Bottom-right corner
        draw.polygon([(self.width - 20, self.height - 20),
                     (self.width - 60, self.height - 20),
                     (self.width - 20, self.height - 60)], fill=accent_color)

        # Add small calendar icon if English
        if language == "en":
            self._draw_calendar_icon(draw)

    def _draw_calendar_icon(self, draw):
        """Draw a small calendar icon"""
        icon_size = 40
        icon_x = self.width - 70
        icon_y = 30

        # Calendar base
        icon_color = tuple(int(self.text_secondary[i:i+2], 16) for i in (1, 3, 5)) + (200,)
        draw.rectangle([icon_x, icon_y, icon_x + icon_size, icon_y + icon_size],
                      fill=icon_color, outline=self.text_primary, width=2)

        # Calendar rings
        ring_color = tuple(int(self.text_primary[i:i+2], 16) for i in (1, 3, 5)) + (255,)
        draw.rectangle([icon_x + 8, icon_y - 5, icon_x + 12, icon_y + 8], fill=ring_color)
        draw.rectangle([icon_x + 28, icon_y - 5, icon_x + 32, icon_y + 8], fill=ring_color)

        # Calendar grid
        for i in range(2):
            y = icon_y + 15 + i * 8
            draw.line([icon_x + 5, y, icon_x + icon_size - 5, y], fill=self.text_primary, width=1)

        for i in range(3):
            x = icon_x + 8 + i * 8
            draw.line([x, icon_y + 10, x, icon_y + icon_size - 5], fill=self.text_primary, width=1)

    def generate_sticker_for_channel(self, channel, date_obj=None):
        """
        Generate a beautiful date sticker for any channel (unified style)

        Args:
            channel: Channel object (language/style settings ignored - unified design)
            date_obj (datetime): Date to display (default: today)

        Returns:
            str: Path to generated sticker image file
        """
        try:
            channel_name = getattr(channel, 'name', 'Unknown')
            logger.info(f"🎨 Generating beautiful date sticker for channel {channel_name}")

            # Always use the same beautiful style for all channels
            return self.generate_date_sticker(date_obj=date_obj)

        except Exception as e:
            logger.error(f"❌ Error generating sticker for channel: {e}", exc_info=True)
            return None
