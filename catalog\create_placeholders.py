#!/usr/bin/env python3
"""
Create placeholder images for MignalyBot catalog
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_placeholder_image(width, height, text, filename, bg_color=(240, 240, 240), text_color=(100, 100, 100)):
    """Create a placeholder image with text"""
    
    # Create image
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a font, fallback to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # Calculate text position (center)
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # Draw text
    draw.text((x, y), text, fill=text_color, font=font)
    
    # Save image
    img.save(filename)
    print(f"Created: {filename}")

def main():
    """Create all placeholder images"""
    
    # Create images directory if it doesn't exist
    images_dir = "assets/images"
    os.makedirs(images_dir, exist_ok=True)
    
    # Define images to create
    images = [
        # Logo and branding
        (200, 60, "MignalyBot", f"{images_dir}/logo.png", (37, 99, 235), (255, 255, 255)),
        
        # Hero section
        (800, 600, "Dashboard Preview", f"{images_dir}/hero-dashboard.png"),
        
        # Benefits illustration
        (600, 400, "Benefits Illustration", f"{images_dir}/benefits-illustration.png"),
        
        # Testimonial avatars
        (100, 100, "AH", f"{images_dir}/testimonial-1.jpg", (37, 99, 235), (255, 255, 255)),
        (100, 100, "SH", f"{images_dir}/testimonial-2.jpg", (245, 158, 11), (255, 255, 255)),
        (100, 100, "MT", f"{images_dir}/testimonial-3.jpg", (16, 185, 129), (255, 255, 255)),
        
        # Feature icons (these would be replaced with actual icons)
        (120, 120, "📈", f"{images_dir}/feature-signals.png"),
        (120, 120, "📰", f"{images_dir}/feature-news.png"),
        (120, 120, "📅", f"{images_dir}/feature-events.png"),
        (120, 120, "☀️", f"{images_dir}/feature-greetings.png"),
        (120, 120, "📊", f"{images_dir}/feature-analysis.png"),
        (120, 120, "🏆", f"{images_dir}/feature-performance.png"),
        
        # Additional marketing images
        (800, 400, "Trading Dashboard", f"{images_dir}/dashboard-screenshot.png"),
        (600, 300, "Mobile App", f"{images_dir}/mobile-preview.png"),
        (400, 300, "Analytics Chart", f"{images_dir}/analytics-chart.png"),
    ]
    
    # Create all images
    for img_data in images:
        if len(img_data) == 4:
            width, height, text, filename = img_data
            create_placeholder_image(width, height, text, filename)
        else:
            width, height, text, filename, bg_color, text_color = img_data
            create_placeholder_image(width, height, text, filename, bg_color, text_color)
    
    print(f"\nCreated {len(images)} placeholder images in {images_dir}/")
    print("Replace these with actual professional images for production use.")

if __name__ == "__main__":
    main()
