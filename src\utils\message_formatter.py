"""
Message formatting utilities for MignalyBot
Handles advertisement footer addition and other message formatting
"""

import logging
import re
from datetime import datetime, timezone
from typing import Optional
from src.database.models import Channel

logger = logging.getLogger(__name__)

# Import language translations
from src.ai_integration.content_generator import PERFORMANCE_CAPTIONS


def get_localized_text(text_key: str, language: str = 'en') -> str:
    """
    Get localized text for a given key and language

    Args:
        text_key (str): The key for the text to translate
        language (str): Language code (en, fa, ar)

    Returns:
        str: Localized text
    """
    lang_captions = PERFORMANCE_CAPTIONS.get(language, PERFORMANCE_CAPTIONS['en'])
    return lang_captions.get(text_key, PERFORMANCE_CAPTIONS['en'].get(text_key, text_key))


def replace_dynamic_countdown(message: str, language: str = 'en') -> str:
    """
    Replace {{DYNAMIC_COUNTDOWN}} placeholder with actual countdown time

    Args:
        message (str): Message content that may contain dynamic countdown placeholder
        language (str): Language code for localized fallback text (en, fa, ar)

    Returns:
        str: Message with dynamic countdown replaced with actual time remaining
    """
    try:
        # Check if message contains dynamic countdown placeholder
        if "{{DYNAMIC_COUNTDOWN}}" not in message:
            return message

        logger.info("🕐 Found dynamic countdown placeholder, calculating actual time remaining...")

        # Extract event time from the message if available
        # Look for hidden event time marker first
        marker_pattern = r'<!-- EVENT_TIME: ([^>]+) -->'
        marker_match = re.search(marker_pattern, message)

        if not marker_match:
            # Fallback: Look for ISO format timestamp in the message
            iso_pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:\+\d{2}:\d{2}|Z)?)'
            iso_match = re.search(iso_pattern, message)
            marker_match = iso_match

        if iso_match:
            try:
                event_time_str = iso_match.group(1)
                # Parse the ISO timestamp
                if event_time_str.endswith('Z'):
                    event_time = datetime.fromisoformat(event_time_str[:-1]).replace(tzinfo=timezone.utc)
                elif '+' in event_time_str:
                    event_time = datetime.fromisoformat(event_time_str)
                else:
                    event_time = datetime.fromisoformat(event_time_str).replace(tzinfo=timezone.utc)

                # Calculate current time remaining
                now_utc = datetime.now(timezone.utc)

                # Ensure event_time is timezone-aware for comparison
                if event_time.tzinfo is None:
                    event_time = event_time.replace(tzinfo=timezone.utc)
                else:
                    event_time = event_time.astimezone(timezone.utc)

                time_until = event_time - now_utc
                minutes_until = int(time_until.total_seconds() / 60)

                logger.info(f"🕐 Event time: {event_time}")
                logger.info(f"🕐 Current time: {now_utc}")
                logger.info(f"🕐 Minutes until event: {minutes_until}")

                # Replace the placeholder with actual countdown
                updated_message = message.replace("{{DYNAMIC_COUNTDOWN}}", str(minutes_until))

                # Remove the hidden marker and any ISO timestamps from the message
                updated_message = re.sub(r'<!-- EVENT_TIME: [^>]+ -->', '', updated_message)
                updated_message = re.sub(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:\+\d{2}:\d{2}|Z)?)', '', updated_message)

                logger.info("✅ Dynamic countdown replaced successfully")
                return updated_message

            except Exception as e:
                logger.error(f"❌ Error parsing event time for dynamic countdown: {e}")
                # Fallback: replace with localized text that handles "min" suffix
                localized_soon = get_localized_text('soon', language)
                updated_message = message.replace("{{DYNAMIC_COUNTDOWN}}min", localized_soon)
                updated_message = updated_message.replace("{{DYNAMIC_COUNTDOWN}}", localized_soon)
                return updated_message
        else:
            logger.warning("⚠️ Dynamic countdown placeholder found but no event time available")
            # Fallback: replace with localized text that handles "min" suffix
            localized_soon = get_localized_text('soon', language)
            updated_message = message.replace("{{DYNAMIC_COUNTDOWN}}min", localized_soon)
            updated_message = updated_message.replace("{{DYNAMIC_COUNTDOWN}}", localized_soon)
            return updated_message

    except Exception as e:
        logger.error(f"❌ Error in dynamic countdown replacement: {e}")
        # Return message with fallback replacement if there's an error
        try:
            localized_soon = get_localized_text('soon', language)
            updated_message = message.replace("{{DYNAMIC_COUNTDOWN}}min", localized_soon)
            updated_message = updated_message.replace("{{DYNAMIC_COUNTDOWN}}", localized_soon)
            return updated_message
        except:
            # If even the fallback fails, return original message
            return message


def add_advertisement_footer(message: str, channel: Channel, parse_mode: str = "HTML") -> str:
    """
    Add advertisement footer to a message if enabled for the channel

    Args:
        message (str): Original message content
        channel (Channel): Channel configuration with advertisement settings
        parse_mode (str): Parse mode for the message (HTML or Markdown)

    Returns:
        str: Message with advertisement footer appended (if enabled)
    """
    try:
        logger.info(f"🔍 Checking advertisement footer for channel: {channel.name} (ID: {channel.id})")

        # Don't add footer to empty messages (for sticker-only posts)
        if not message.strip():
            logger.info(f"⏭️ Skipping advertisement footer for empty message")
            return message

        # Check if advertisement is enabled for this channel
        enable_advertisement = getattr(channel, 'enable_advertisement', False)
        logger.info(f"📢 Advertisement enabled: {enable_advertisement}")

        if not enable_advertisement:
            logger.info(f"❌ Advertisement disabled for channel {channel.name}, returning original message")
            return message

        # Get advertisement text and URL
        ad_text = getattr(channel, 'advertisement_text', 'This message generated by Mignaly')
        ad_url = getattr(channel, 'advertisement_url', 'https://mignaly.com')

        logger.info(f"📝 Advertisement text: '{ad_text}'")
        logger.info(f"🔗 Advertisement URL: '{ad_url}'")

        # Ensure we have valid advertisement data
        if not ad_text or not ad_url:
            logger.warning(f"⚠️ Advertisement enabled for channel {channel.name} but missing text or URL")
            return message

        # Safely handle parse_mode - check for None and provide default
        if parse_mode is None:
            parse_mode = "HTML"
            logger.info(f"🔧 Parse mode was None, defaulting to HTML")

        # Format the advertisement footer based on parse mode
        if parse_mode.upper() == "HTML":
            # HTML format: <a href="url">text</a> - makes the entire text clickable without showing URL
            advertisement_footer = f'\n\n<a href="{ad_url}">{ad_text}</a>'
        else:
            # Markdown format: [text](url) - fallback for compatibility
            advertisement_footer = f"\n\n[{ad_text}]({ad_url})"

        logger.info(f"🏷️ Generated advertisement footer ({parse_mode}): '{advertisement_footer}'")

        # Append the footer to the message
        formatted_message = message + advertisement_footer

        logger.info(f"✅ Added advertisement footer to message for channel {channel.name}")
        logger.info(f"📏 Original message length: {len(message)}, Final message length: {len(formatted_message)}")
        logger.info(f"📋 Final message preview: {formatted_message[:200]}..." if len(formatted_message) > 200 else f"📋 Final message: {formatted_message}")

        return formatted_message

    except Exception as e:
        logger.error(f"💥 Error adding advertisement footer for channel {getattr(channel, 'name', 'unknown')}: {e}")
        # Return original message if there's an error
        return message


def format_message_for_channel(message: str, channel: Channel, parse_mode: str = "Markdown") -> str:
    """
    Apply all channel-specific formatting to a message

    Args:
        message (str): Original message content
        channel (Channel): Channel configuration
        parse_mode (str): Parse mode for the message (Markdown or HTML)

    Returns:
        str: Fully formatted message ready for sending
    """
    try:
        # Safely handle parse_mode - check for None and provide default
        if parse_mode is None:
            parse_mode = "Markdown"
            logger.info(f"🔧 Parse mode was None, defaulting to Markdown")

        logger.info(f"🎨 Formatting message for channel: {channel.name} (Parse mode: {parse_mode})")
        logger.info(f"📝 Original message length: {len(message)}")

        # Start with the original message
        formatted_message = message

        # Replace dynamic countdown placeholders first
        logger.info(f"🕐 Checking for dynamic countdown placeholders...")
        channel_language = getattr(channel, 'language', 'en')
        formatted_message = replace_dynamic_countdown(formatted_message, channel_language)

        # Add advertisement footer if enabled
        logger.info(f"📢 Calling add_advertisement_footer for channel {channel.name}")
        formatted_message = add_advertisement_footer(formatted_message, channel, parse_mode)

        # Future formatting features can be added here
        # e.g., custom signatures, channel-specific formatting, etc.

        logger.info(f"✅ Message formatting completed for channel {channel.name}")
        logger.info(f"📏 Final formatted message length: {len(formatted_message)}")

        return formatted_message

    except Exception as e:
        logger.error(f"💥 Error formatting message for channel {getattr(channel, 'name', 'unknown')}: {e}")
        # Return original message if there's an error
        return message