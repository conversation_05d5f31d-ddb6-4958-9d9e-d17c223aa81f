"""
Admin dashboard server for MignalyBot
"""

import os
import logging
import asyncio
from datetime import datetime, timezone
import uvicorn
from fastapi import FastAPI, Request, Depends, HTTPException, status
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
import secrets

# Configure logging FIRST before importing other modules
from src.utils.logging_config import setup_logging
logger = setup_logging(log_level=os.getenv("LOG_LEVEL", "INFO"))

from src.database.setup import get_async_db
from src.strategies.default_strategies import create_default_strategies
from src.admin.routes import router as api_router
from src.utils.duplicate_cleaner import delete_duplicate_events, get_duplicate_events_count

# Create FastAPI app
app = FastAPI(title="MignalyBot Admin")

# Log startup information
logger.info("🚀 Starting MignalyBot Admin Server")
logger.debug("🔍 DEBUG: Debug logging is enabled in admin server!")
logger.info(f"📊 Log level: {logger.getEffectiveLevel()}")
logger.info(f"🌍 Environment: DEBUG={os.getenv('DEBUG')}, LOG_LEVEL={os.getenv('LOG_LEVEL')}")

# Set up security
security = HTTPBasic()

# Set up templates
templates_dir = os.path.join(os.path.dirname(__file__), "templates")
templates = Jinja2Templates(directory=templates_dir)

# Set up static files
static_dir = os.path.join(os.path.dirname(__file__), "static")
os.makedirs(static_dir, exist_ok=True)
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Include API routes
app.include_router(api_router, prefix="/api")

# Health check endpoint for Docker
@app.get("/health")
async def health_check():
    """Health check endpoint for Docker containers"""
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}

# Startup event handler
@app.on_event("startup")
async def startup_event():
    """Handle application startup tasks"""
    logger.info("Application startup - running cleanup tasks...")

    try:
        # Check for duplicate events
        duplicate_count = await get_duplicate_events_count()
        if duplicate_count > 0:
            logger.info(f"Found {duplicate_count} duplicate events, cleaning up...")
            deleted_count = await delete_duplicate_events()
            logger.info(f"Startup cleanup completed: deleted {deleted_count} duplicate events")
        else:
            logger.info("No duplicate events found during startup")
    except Exception as e:
        logger.error(f"Error during startup cleanup: {e}", exc_info=True)

# Get admin credentials from environment variables
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "admin")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "admin")

def verify_credentials(credentials: HTTPBasicCredentials = Depends(security)):
    """Verify HTTP basic auth credentials"""
    correct_username = secrets.compare_digest(credentials.username, ADMIN_USERNAME)
    correct_password = secrets.compare_digest(credentials.password, ADMIN_PASSWORD)

    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    return credentials.username

@app.get("/", response_class=HTMLResponse)
async def index(request: Request, username: str = Depends(verify_credentials)):
    """Render the admin dashboard index page"""
    return templates.TemplateResponse(
        "index.html",
        {"request": request, "username": username}
    )

@app.get("/config", response_class=HTMLResponse)
async def config_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the configuration page"""
    return templates.TemplateResponse(
        "config.html",
        {"request": request, "username": username}
    )

@app.get("/channels", response_class=HTMLResponse)
async def channels_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the channels page"""
    return templates.TemplateResponse(
        "channels.html",
        {"request": request, "username": username}
    )

@app.get("/strategies", response_class=HTMLResponse)
async def strategies_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the strategies page"""
    return templates.TemplateResponse(
        "strategies.html",
        {"request": request, "username": username}
    )

@app.get("/prompts", response_class=HTMLResponse)
async def prompts_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the prompt templates page"""
    return templates.TemplateResponse(
        "prompts.html",
        {"request": request, "username": username}
    )

@app.get("/posts", response_class=HTMLResponse)
async def posts_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the posts page"""
    return templates.TemplateResponse(
        "posts.html",
        {"request": request, "username": username}
    )

@app.get("/signals", response_class=HTMLResponse)
async def signals_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the signals page"""
    return templates.TemplateResponse(
        "signals.html",
        {"request": request, "username": username}
    )

@app.get("/news", response_class=HTMLResponse)
async def news_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the news page"""
    return templates.TemplateResponse(
        "news.html",
        {"request": request, "username": username}
    )

@app.get("/events", response_class=HTMLResponse)
async def events_page(request: Request, username: str = Depends(verify_credentials)):
    """Render the events page"""
    return templates.TemplateResponse(
        "events.html",
        {"request": request, "username": username}
    )

@app.get("/health")
async def health_check():
    """Health check endpoint for Docker"""
    try:
        # Check database connection
        async for db in get_async_db():
            # Simple query to test database
            from sqlalchemy import text
            result = db.execute(text("SELECT 1"))
            result.fetchone()
            break

        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "mignalybot"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")

async def init_admin():
    """Initialize admin components"""
    logger.info("Initializing admin components")

    # Create default strategies
    await create_default_strategies()

    logger.info("Admin components initialized")

async def start_admin_server():
    """Start the admin dashboard server"""
    logger.info("Starting admin dashboard server")

    # Initialize admin components
    await init_admin()

    # Start the server
    # Get port from environment variable or use default
    port = int(os.getenv("PORT", 8000))
    config = uvicorn.Config(
        app=app,
        host="0.0.0.0",
        port=port,
        log_level="info"
    )
    server = uvicorn.Server(config)

    await server.serve()

    logger.info("Admin dashboard server stopped")
