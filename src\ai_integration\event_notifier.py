"""
Economic Event Notification System
Sends pre-event and post-event notifications with AI analysis
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Optional
from sqlalchemy import select, and_

from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import EconomicEvent, Channel, Post, PostType, PostStatus
from src.ai_integration.qwen_client import Qwen<PERSON>lient
from src.telegram.bot import send_message_to_channel

logger = logging.getLogger(__name__)

class EventNotifier:
    """Handles economic event notifications"""
    
    def __init__(self):
        self.qwen_client = QwenClient()
    
    async def check_and_send_notifications(self):
        """Check for upcoming events and send notifications"""
        logger.info("Checking for economic events requiring notifications")
        
        try:
            # Get events happening in the next 5-30 minutes
            now = datetime.now(timezone.utc)
            start_time = now + timedelta(minutes=5)
            end_time = now + timedelta(minutes=30)
            
            upcoming_events = await self.get_upcoming_events(start_time, end_time)
            
            if upcoming_events:
                logger.info(f"Found {len(upcoming_events)} upcoming events")
                await self.send_pre_event_notifications(upcoming_events)
            
            # Check for events that happened 5-60 minutes ago for post-event analysis
            past_start = now - timedelta(minutes=60)
            past_end = now - timedelta(minutes=5)
            
            past_events = await self.get_past_events(past_start, past_end)
            
            if past_events:
                logger.info(f"Found {len(past_events)} recent past events")
                await self.send_post_event_notifications(past_events)
                
        except Exception as e:
            logger.error(f"Error in event notification check: {e}", exc_info=True)
    
    async def get_upcoming_events(self, start_time: datetime, end_time: datetime) -> List[EconomicEvent]:
        """Get events happening between start_time and end_time"""
        events = []

        # Ensure datetimes are timezone-aware for database comparison
        # Since event_time is now stored as timezone-aware (UTC), we need timezone-aware datetimes
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)

        async for db in get_async_db():
            try:
                if is_sqlite_db():
                    result = db.execute(
                        select(EconomicEvent).where(
                            and_(
                                EconomicEvent.event_time >= start_time,
                                EconomicEvent.event_time <= end_time,
                                EconomicEvent.impact >= 2  # Medium to high impact only
                            )
                        ).order_by(EconomicEvent.event_time)
                    )
                else:
                    result = await db.execute(
                        select(EconomicEvent).where(
                            and_(
                                EconomicEvent.event_time >= start_time,
                                EconomicEvent.event_time <= end_time,
                                EconomicEvent.impact >= 2  # Medium to high impact only
                            )
                        ).order_by(EconomicEvent.event_time)
                    )
                
                events = result.scalars().all()
                break
                
            except Exception as e:
                logger.error(f"Error getting upcoming events: {e}")
                break
        
        return events
    
    async def get_past_events(self, start_time: datetime, end_time: datetime) -> List[EconomicEvent]:
        """Get events that happened between start_time and end_time"""
        events = []

        # Ensure datetimes are timezone-aware for database comparison
        # Since event_time is now stored as timezone-aware (UTC), we need timezone-aware datetimes
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)

        async for db in get_async_db():
            try:
                if is_sqlite_db():
                    result = db.execute(
                        select(EconomicEvent).where(
                            and_(
                                EconomicEvent.event_time >= start_time,
                                EconomicEvent.event_time <= end_time,
                                EconomicEvent.impact >= 2,  # Medium to high impact only
                                EconomicEvent.actual.isnot(None)  # Only events with actual results
                            )
                        ).order_by(EconomicEvent.event_time.desc())
                    )
                else:
                    result = await db.execute(
                        select(EconomicEvent).where(
                            and_(
                                EconomicEvent.event_time >= start_time,
                                EconomicEvent.event_time <= end_time,
                                EconomicEvent.impact >= 2,  # Medium to high impact only
                                EconomicEvent.actual.isnot(None)  # Only events with actual results
                            )
                        ).order_by(EconomicEvent.event_time.desc())
                    )
                
                events = result.scalars().all()
                break
                
            except Exception as e:
                logger.error(f"Error getting past events: {e}")
                break
        
        return events
    
    async def send_pre_event_notifications(self, events: List[EconomicEvent]):
        """Send pre-event notifications to all configured channels"""
        channels = await self.get_active_channels()
        
        for event in events:
            # Check if we already sent notification for this event
            if await self.notification_already_sent(event, "pre"):
                continue
                
            for channel in channels:
                try:
                    # Generate pre-event analysis
                    analysis = await self.generate_pre_event_analysis(event, channel.language, channel.brand_name)

                    if analysis:
                        # Send to channel
                        await send_message_to_channel(channel.chat_id, analysis)

                        # Save as post record
                        await self.save_notification_post(channel, event, analysis, "pre")

                        logger.info(f"Sent pre-event notification for {event.title} to {channel.name}")

                except Exception as e:
                    logger.error(f"Error sending pre-event notification: {e}")
    
    async def send_post_event_notifications(self, events: List[EconomicEvent]):
        """Send post-event notifications with actual results"""
        channels = await self.get_active_channels()
        
        for event in events:
            # Check if we already sent post-event notification
            if await self.notification_already_sent(event, "post"):
                continue
                
            for channel in channels:
                try:
                    # Generate post-event analysis
                    analysis = await self.generate_post_event_analysis(event, channel.language, channel.brand_name)

                    if analysis:
                        # Send to channel
                        await send_message_to_channel(channel.chat_id, analysis)

                        # Save as post record
                        await self.save_notification_post(channel, event, analysis, "post")

                        logger.info(f"Sent post-event notification for {event.title} to {channel.name}")

                except Exception as e:
                    logger.error(f"Error sending post-event notification: {e}")
    
    async def generate_pre_event_analysis(self, event: EconomicEvent, language: str = "en", channel_brand: str = None) -> str:
        """Generate short pre-event analysis"""
        try:
            # Create impact emoji
            impact_emoji = "🔴" if event.impact == 3 else "🟡" if event.impact == 2 else "🟢"

            # Ensure event_time is timezone-aware for calculation
            if event.event_time.tzinfo is None:
                # If event_time is naive, assume it's in UTC
                event_time_utc = event.event_time.replace(tzinfo=timezone.utc)
            else:
                # If it's already timezone-aware, convert to UTC
                event_time_utc = event.event_time.astimezone(timezone.utc)

            # Time until event
            time_diff = event_time_utc - datetime.now(timezone.utc)
            minutes_until = int(time_diff.total_seconds() / 60)

            # Process channel brand for handle
            if channel_brand:
                # Clean up channel brand for handle - remove spaces, convert to lowercase
                # Handle common patterns like 'bot' -> 'fx', remove special characters
                channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
                # Remove any non-alphanumeric characters except underscores
                import re
                channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
            else:
                channel_handle = 'tradingchannel'

            prompt = f"""Generate a very short pre-event alert (max 4 lines) for:

Event: {event.title}
Country: {event.country}
Currency: {event.currency}
Impact: {impact_emoji} {'High' if event.impact == 3 else 'Medium' if event.impact == 2 else 'Low'}
Time: {minutes_until} minutes
Previous: {event.previous or 'N/A'}
Forecast: {event.forecast or 'N/A'}
Channel Brand: {channel_brand or 'Trading Channel'}

Format:
🚨 [EVENT NAME] in {minutes_until}min
📊 Forecast: [value] | Previous: [value]
💡 Brief market impact expectation
📱 @{channel_handle}

Language: {language}
Keep it under 200 characters total."""

            analysis = await self.qwen_client.generate_content(prompt, max_tokens=200, language=language)
            return analysis.strip() if analysis else None

        except Exception as e:
            logger.error(f"Error generating pre-event analysis: {e}")
            return None
    
    async def generate_post_event_analysis(self, event: EconomicEvent, language: str = "en", channel_brand: str = None) -> str:
        """Generate short post-event analysis with actual results"""
        try:
            # Create impact emoji
            impact_emoji = "🔴" if event.impact == 3 else "🟡" if event.impact == 2 else "🟢"

            # Compare actual vs forecast
            surprise = ""
            if event.actual and event.forecast:
                try:
                    actual_val = float(str(event.actual).replace('%', ''))
                    forecast_val = float(str(event.forecast).replace('%', ''))
                    if actual_val > forecast_val:
                        surprise = "📈 Above forecast"
                    elif actual_val < forecast_val:
                        surprise = "📉 Below forecast"
                    else:
                        surprise = "🎯 As expected"
                except:
                    surprise = ""

            # Process channel brand for handle
            if channel_brand:
                # Clean up channel brand for handle - remove spaces, convert to lowercase
                # Handle common patterns like 'bot' -> 'fx', remove special characters
                channel_handle = channel_brand.lower().replace(' ', '').replace('bot', 'fx')
                # Remove any non-alphanumeric characters except underscores
                import re
                channel_handle = re.sub(r'[^a-zA-Z0-9_]', '', channel_handle)
            else:
                channel_handle = 'tradingchannel'

            prompt = f"""Generate a very short post-event result (max 4 lines) for:

Event: {event.title}
Country: {event.country}
Currency: {event.currency}
Impact: {impact_emoji}
Actual: {event.actual or 'N/A'}
Forecast: {event.forecast or 'N/A'}
Previous: {event.previous or 'N/A'}
{surprise}
Channel Brand: {channel_brand or 'Trading Channel'}

Format:
✅ [EVENT NAME] Released
📊 Actual: [value] | Forecast: [value]
💡 Brief market reaction/impact
📱 @{channel_handle}

Language: {language}
Keep it under 200 characters total."""

            analysis = await self.qwen_client.generate_content(prompt, max_tokens=200, language=language)
            return analysis.strip() if analysis else None

        except Exception as e:
            logger.error(f"Error generating post-event analysis: {e}")
            return None
    
    async def get_active_channels(self) -> List[Channel]:
        """Get all active channels"""
        channels = []
        
        async for db in get_async_db():
            try:
                if is_sqlite_db():
                    result = db.execute(
                        select(Channel).where(Channel.active == True)
                    )
                else:
                    result = await db.execute(
                        select(Channel).where(Channel.active == True)
                    )
                
                channels = result.scalars().all()
                break
                
            except Exception as e:
                logger.error(f"Error getting active channels: {e}")
                break
        
        return channels
    
    async def notification_already_sent(self, event: EconomicEvent, notification_type: str) -> bool:
        """Check if notification was already sent for this event"""
        async for db in get_async_db():
            try:
                # Check for existing posts with this event
                if is_sqlite_db():
                    result = db.execute(
                        select(Post).where(
                            and_(
                                Post.event_id == event.id,
                                Post.content.contains(f"{notification_type}-event")
                            )
                        )
                    )
                else:
                    result = await db.execute(
                        select(Post).where(
                            and_(
                                Post.event_id == event.id,
                                Post.content.contains(f"{notification_type}-event")
                            )
                        )
                    )
                
                existing_post = result.scalars().first()
                return existing_post is not None
                
            except Exception as e:
                logger.error(f"Error checking notification status: {e}")
                return False
    
    async def save_notification_post(self, channel: Channel, event: EconomicEvent, content: str, notification_type: str):
        """Save notification as a post record"""
        async for db in get_async_db():
            try:
                post = Post(
                    channel_id=channel.id,
                    type=PostType.EVENT,
                    content=f"[{notification_type}-event] {content}",
                    status=PostStatus.PUBLISHED,  # Use PUBLISHED instead of SENT
                    event_id=event.id,
                    created_at=datetime.now(timezone.utc),
                    published_time=datetime.now(timezone.utc)  # Use published_time instead of sent_at
                )
                
                db.add(post)
                if is_sqlite_db():
                    db.commit()
                else:
                    await db.commit()
                    
                logger.info(f"✅ Saved notification post for event {event.title} to channel {channel.name}")

            except Exception as e:
                logger.error(f"Error saving notification post for event {event.title} to channel {channel.name}: {e}")
                try:
                    if is_sqlite_db():
                        db.rollback()
                    else:
                        await db.rollback()
                except Exception as rollback_error:
                    logger.error(f"Error during rollback: {rollback_error}")


# Main function to be called by scheduler
async def check_event_notifications():
    """Main function to check and send event notifications"""
    notifier = EventNotifier()
    await notifier.check_and_send_notifications()
