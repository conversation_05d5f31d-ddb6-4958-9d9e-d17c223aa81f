#!/usr/bin/env python3
"""
Database table creation script for MignalyBot
"""

import asyncio
import logging
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.database.setup import engine, Base, get_async_db, is_sqlite_db
from src.database.models import *  # Import all models
from sqlalchemy import text

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def create_tables():
    """Create all database tables"""
    try:
        logger.info("Creating database tables...")
        
        if is_sqlite_db():
            # For SQLite, create tables synchronously
            Base.metadata.create_all(bind=engine)
            logger.info("✅ SQLite tables created successfully")
        else:
            # For async databases (PostgreSQL, etc.)
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("✅ Database tables created successfully")
            
        # Test database connection
        async for db in get_async_db():
            try:
                if is_sqlite_db():
                    # Test SQLite connection
                    result = db.execute(text("SELECT 1"))
                    result.fetchone()
                else:
                    # Test async connection
                    result = await db.execute(text("SELECT 1"))
                    await result.fetchone()

                logger.info("✅ Database connection test successful")
                break

            except Exception as e:
                logger.error(f"❌ Database connection test failed: {e}")
                raise
                
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating tables: {e}")
        return False

async def main():
    """Main function"""
    logger.info("Starting database table creation")
    
    try:
        success = await create_tables()
        
        if success:
            logger.info("🎉 Database setup completed successfully!")
        else:
            logger.error("💥 Database setup failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Database setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
