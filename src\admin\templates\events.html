{% extends "base.html" %}

{% block title %}Economic Events - MignalyBot Admin{% endblock %}

{% block page_title %}Economic Events Management{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Economic Calendar</h5>
        <div>
            <button type="button" class="btn btn-primary" id="refreshEventsBtn">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button type="button" class="btn btn-success" id="collectEventsBtn">
                <i class="fas fa-download"></i> Collect Events
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" class="form-control" id="searchEvents" placeholder="Search events...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="countryFilter">
                        <option value="">All Countries</option>
                        <!-- Countries will be populated dynamically -->
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="impactFilter">
                        <option value="">All Impact Levels</option>
                        <option value="3">High Impact</option>
                        <option value="2">Medium Impact</option>
                        <option value="1">Low Impact</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover" id="eventsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Event</th>
                        <th>Country</th>
                        <th>Impact</th>
                        <th>Date/Time</th>
                        <th>Forecast</th>
                        <th>Previous</th>
                        <th>Actual</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center">Loading events...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div>
                <span id="eventsCount">0</span> economic events
            </div>
            <div>
                <nav aria-label="Events pagination">
                    <ul class="pagination" id="eventsPagination">
                        <!-- Pagination will be populated dynamically -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Event Detail Modal -->
<div class="modal fade" id="eventDetailModal" tabindex="-1" aria-labelledby="eventDetailModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventDetailModalTitle">Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h5 id="eventTitle"></h5>
                    <div class="d-flex justify-content-between text-muted small">
                        <div>Country: <span id="eventCountry"></span></div>
                        <div>Currency: <span id="eventCurrency"></span></div>
                        <div>Date/Time: <span id="eventDateTime"></span></div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">Impact</div>
                            <div class="card-body text-center" id="eventImpact"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">Forecast</div>
                            <div class="card-body text-center" id="eventForecast"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">Previous</div>
                            <div class="card-body text-center" id="eventPrevious"></div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>Actual Result</h6>
                    <div id="eventActual" class="p-3 bg-light rounded text-center"></div>
                </div>
                
                <div class="mb-3">
                    <h6>AI Analysis</h6>
                    <div id="eventAnalysis" class="p-3 bg-light rounded"></div>
                </div>
                
                <div class="mb-3">
                    <button id="generateEventPostBtn" class="btn btn-sm btn-success">
                        <i class="fas fa-magic"></i> Generate Post
                    </button>
                    <button id="generateCountdownBtn" class="btn btn-sm btn-info">
                        <i class="fas fa-clock"></i> Generate Countdown
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Event Confirmation Modal -->
<div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalTitle" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEventModalTitle">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the event "<strong id="deleteEventName"></strong>"?</p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone and will also delete any associated posts.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Event</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    const pageSize = 20;
    let totalEvents = 0;
    
    $(document).ready(function() {
        // Load events
        loadEvents();
        
        // Set up event handlers
        $('#refreshEventsBtn').click(function() {
            loadEvents();
        });
        
        $('#collectEventsBtn').click(function() {
            collectEvents();
        });
        
        $('#searchEvents, #countryFilter, #impactFilter').on('change keyup', function() {
            currentPage = 1;
            loadEvents();
        });
        
        // Initialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
    });
    
    function loadEvents() {
        const search = $('#searchEvents').val();
        const country = $('#countryFilter').val();
        const impact = $('#impactFilter').val();
        
        $.ajax({
            url: '/api/events',
            type: 'GET',
            data: {
                search: search,
                country: country,
                impact: impact,
                page: currentPage,
                page_size: pageSize
            },
            success: function(data) {
                totalEvents = data.total;
                $('#eventsCount').text(data.total);
                
                let tableHtml = '';
                
                if (data.events && data.events.length > 0) {
                    data.events.forEach(function(event) {
                        const eventDate = new Date(event.event_time).toLocaleString();
                        const impactBadge = getImpactBadge(event.impact);
                        
                        tableHtml += `
                            <tr>
                                <td>${event.id}</td>
                                <td>
                                    <a href="#" class="event-detail-link" data-event-id="${event.id}">
                                        ${event.title}
                                    </a>
                                </td>
                                <td>${event.country || 'N/A'}</td>
                                <td>${impactBadge}</td>
                                <td>${eventDate}</td>
                                <td>${event.forecast || 'N/A'}</td>
                                <td>${event.previous || 'N/A'}</td>
                                <td>${event.actual || 'N/A'}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary event-detail-btn" data-event-id="${event.id}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-success generate-event-post-btn" data-event-id="${event.id}">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-event-btn" data-event-id="${event.id}" data-event-title="${event.title}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                    });
                } else {
                    tableHtml = '<tr><td colspan="9" class="text-center">No events found</td></tr>';
                }
                
                $('#eventsTable tbody').html(tableHtml);
                
                // Set up pagination
                updatePagination(data.total);
                
                // Set up event handlers for the newly created elements
                $('.event-detail-link, .event-detail-btn').click(function() {
                    const eventId = $(this).data('event-id');
                    showEventDetail(eventId);
                });
                
                $('.generate-event-post-btn').click(function() {
                    const eventId = $(this).data('event-id');
                    generateEventPost(eventId);
                });

                $('.delete-event-btn').click(function() {
                    const eventId = $(this).data('event-id');
                    const eventTitle = $(this).data('event-title');
                    showDeleteConfirmation(eventId, eventTitle);
                });
                
                // Populate country filter if it's empty
                if ($('#countryFilter option').length <= 1) {
                    const countries = [...new Set(data.events.map(item => item.country).filter(Boolean))];
                    let countryOptions = '<option value="">All Countries</option>';
                    countries.forEach(country => {
                        countryOptions += `<option value="${country}">${country}</option>`;
                    });
                    $('#countryFilter').html(countryOptions);
                }
            },
            error: function(xhr) {
                $('#eventsTable tbody').html('<tr><td colspan="9" class="text-center text-danger">Error loading events</td></tr>');
                console.error('Error loading events:', xhr);
            }
        });
    }
    
    function getImpactBadge(impact) {
        if (impact === 3) {
            return '<span class="badge bg-danger">High</span>';
        } else if (impact === 2) {
            return '<span class="badge bg-warning text-dark">Medium</span>';
        } else if (impact === 1) {
            return '<span class="badge bg-info">Low</span>';
        } else {
            return '<span class="badge bg-secondary">Unknown</span>';
        }
    }
    
    function updatePagination(total) {
        const totalPages = Math.ceil(total / pageSize);
        let paginationHtml = '';
        
        if (totalPages > 1) {
            // Previous button
            paginationHtml += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>
                </li>
            `;
            
            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHtml += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHtml += `
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    `;
                }
            }
            
            // Next button
            paginationHtml += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>
                </li>
            `;
        }
        
        $('#eventsPagination').html(paginationHtml);
        
        // Set up event handlers for pagination
        $('.page-link').click(function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            if (page && page !== currentPage && page > 0 && page <= totalPages) {
                currentPage = page;
                loadEvents();
            }
        });
    }
    
    function showEventDetail(eventId) {
        $.ajax({
            url: `/api/events/${eventId}`,
            type: 'GET',
            success: function(event) {
                $('#eventTitle').text(event.title);
                $('#eventCountry').text(event.country || 'N/A');
                $('#eventCurrency').text(event.currency || 'N/A');
                $('#eventDateTime').text(new Date(event.event_time).toLocaleString());
                $('#eventImpact').html(getImpactBadge(event.impact));
                $('#eventForecast').text(event.forecast || 'N/A');
                $('#eventPrevious').text(event.previous || 'N/A');
                $('#eventActual').text(event.actual || 'Not Available');
                $('#eventAnalysis').html(event.ai_analysis || '<em>No AI analysis available</em>');
                
                // Set up generate post buttons
                $('#generateEventPostBtn').data('event-id', event.id);
                $('#generateCountdownBtn').data('event-id', event.id);
                
                // Show/hide countdown button based on event time
                const eventTime = new Date(event.event_time);
                const now = new Date();
                if (eventTime > now) {
                    $('#generateCountdownBtn').show();
                } else {
                    $('#generateCountdownBtn').hide();
                }
                
                // Show modal
                $('#eventDetailModal').modal('show');
            },
            error: function(xhr) {
                alert('Error loading event details: ' + xhr.responseJSON.detail);
            }
        });
    }
    
    function generateEventPost(eventId) {
        // Get available channels first
        $.ajax({
            url: '/api/channels',
            type: 'GET',
            success: function(channels) {
                if (channels.length === 0) {
                    alert('No active channels found. Please create and activate a channel first.');
                    return;
                }

                // If only one channel, use it directly
                if (channels.length === 1) {
                    generateEventPostForChannel(eventId, channels[0].id);
                    return;
                }

                // Show channel selection modal
                let html = `
                    <div class="modal fade" id="eventChannelSelectModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Select Channel</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="eventChannelSelect" class="form-label">Choose a channel to generate the event post:</label>
                                        <select class="form-select" id="eventChannelSelect">
                `;

                channels.forEach(channel => {
                    html += `<option value="${channel.id}">${channel.name} (${channel.language})</option>`;
                });

                html += `
                                        </select>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" onclick="generateEventPostForChannel(${eventId}, $('#eventChannelSelect').val())">Generate Post</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#eventChannelSelectModal').remove();
                $('body').append(html);
                $('#eventChannelSelectModal').modal('show');
            },
            error: function() {
                alert('Error loading channels');
            }
        });
    }

    function generateEventPostForChannel(eventId, channelId) {
        $('#eventChannelSelectModal').modal('hide');

        $.ajax({
            url: '/api/events/generate-post',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ event_id: eventId, channel_id: parseInt(channelId) }),
            success: function(response) {
                alert('Post generated successfully!');
                // Redirect to posts page
                window.location.href = '/posts';
            },
            error: function(xhr) {
                alert('Error generating post: ' + xhr.responseJSON.detail);
            }
        });
    }

    function generateCountdown(eventId) {
        // Get available channels first
        $.ajax({
            url: '/api/channels',
            type: 'GET',
            success: function(channels) {
                if (channels.length === 0) {
                    alert('No active channels found. Please create and activate a channel first.');
                    return;
                }

                // If only one channel, use it directly
                if (channels.length === 1) {
                    generateCountdownForChannel(eventId, channels[0].id);
                    return;
                }

                // Show channel selection modal
                let html = `
                    <div class="modal fade" id="countdownChannelSelectModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Select Channel</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="countdownChannelSelect" class="form-label">Choose a channel to generate the countdown post:</label>
                                        <select class="form-select" id="countdownChannelSelect">
                `;

                channels.forEach(channel => {
                    html += `<option value="${channel.id}">${channel.name} (${channel.language})</option>`;
                });

                html += `
                                        </select>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" onclick="generateCountdownForChannel(${eventId}, $('#countdownChannelSelect').val())">Generate Countdown</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#countdownChannelSelectModal').remove();
                $('body').append(html);
                $('#countdownChannelSelectModal').modal('show');
            },
            error: function() {
                alert('Error loading channels');
            }
        });
    }

    function generateCountdownForChannel(eventId, channelId) {
        $('#countdownChannelSelectModal').modal('hide');

        $.ajax({
            url: '/api/events/generate-countdown',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ event_id: eventId, channel_id: parseInt(channelId) }),
            success: function(response) {
                alert('Countdown post generated successfully!');
                // Redirect to posts page
                window.location.href = '/posts';
            },
            error: function(xhr) {
                alert('Error generating countdown post: ' + xhr.responseJSON.detail);
            }
        });
    }
    
    function collectEvents() {
        $.ajax({
            url: '/api/actions/collect-events',
            type: 'POST',
            success: function(response) {
                alert('Economic events collection started successfully!');
            },
            error: function(xhr) {
                alert('Error: ' + xhr.responseJSON.detail);
            }
        });
    }
    
    // Set up event handlers for the modal buttons
    $(document).on('click', '#generateEventPostBtn', function() {
        const eventId = $(this).data('event-id');
        generateEventPost(eventId);
    });
    
    $(document).on('click', '#generateCountdownBtn', function() {
        const eventId = $(this).data('event-id');
        generateCountdown(eventId);
    });

    function showDeleteConfirmation(eventId, eventTitle) {
        $('#deleteEventName').text(eventTitle);
        $('#confirmDeleteBtn').data('event-id', eventId);
        $('#deleteEventModal').modal('show');

        // Set up delete confirmation button
        $('#confirmDeleteBtn').off('click').click(function() {
            const id = $(this).data('event-id');
            deleteEvent(id);
        });
    }

    function deleteEvent(eventId) {
        $.ajax({
            url: `/api/events/${eventId}`,
            type: 'DELETE',
            success: function(response) {
                $('#deleteEventModal').modal('hide');
                loadEvents();
                alert('Event deleted successfully!');
            },
            error: function(xhr) {
                alert('Error deleting event: ' + xhr.responseJSON.detail);
            }
        });
    }
</script>
{% endblock %}
