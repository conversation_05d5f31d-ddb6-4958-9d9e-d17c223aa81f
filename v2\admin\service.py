"""
Admin interface service for MignalyBot v2

Features:
- FastAPI-based REST API
- Health monitoring endpoints
- Configuration management
- Performance metrics
- Content management
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBasi<PERSON>, HTTPBasicCredentials
from fastapi.responses import JSONResponse
import secrets

from core.config.settings import Settings
from core.utils.helpers import async_performance_monitor
from database.connection.manager import DatabaseManager
from database.models.core import Channel, Config
from ai.clients.qwen import QwenClient
from data.service import DataCollectionService
from telegram_bot.service import TelegramService
from services.content import ContentService


class AdminService:
    """Admin interface service"""
    
    def __init__(
        self,
        settings: Settings,
        db_manager: DatabaseManager,
        qwen_client: Optional[QwenClient] = None,
        data_service: Optional[DataCollectionService] = None,
        telegram_service: Optional[TelegramService] = None,
        content_service: Optional[ContentService] = None
    ):
        self.settings = settings
        self.db_manager = db_manager
        self.qwen_client = qwen_client
        self.data_service = data_service
        self.telegram_service = telegram_service
        self.content_service = content_service
        self.logger = logging.getLogger(__name__)
        
        # FastAPI app
        self.app = FastAPI(
            title="MignalyBot v2 Admin API",
            description="Admin interface for MignalyBot v2",
            version="2.0.0"
        )
        
        # Security
        self.security = HTTPBasic()
        
        # Setup middleware and routes
        self._setup_middleware()
        self._setup_routes()
        
        # Service state
        self.running = False
        self.server_task = None
    
    def _setup_middleware(self):
        """Setup FastAPI middleware"""
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.settings.admin.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_routes(self):
        """Setup API routes"""
        
        def verify_credentials(credentials: HTTPBasicCredentials = Depends(self.security)):
            """Verify admin credentials"""
            correct_username = secrets.compare_digest(
                credentials.username, self.settings.admin.username
            )
            correct_password = secrets.compare_digest(
                credentials.password, self.settings.admin.password
            )
            if not (correct_username and correct_password):
                raise HTTPException(
                    status_code=401,
                    detail="Invalid credentials",
                    headers={"WWW-Authenticate": "Basic"},
                )
            return credentials
        
        @self.app.get("/")
        async def root():
            """Root endpoint"""
            return {
                "name": "MignalyBot v2 Admin API",
                "version": "2.0.0",
                "status": "running",
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/health")
        async def health_check():
            """System health check"""
            health_data = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "components": {}
            }
            
            # Database health
            if self.db_manager:
                db_health = await self.db_manager.health_check()
                health_data["components"]["database"] = db_health
            
            # AI client health
            if self.qwen_client:
                ai_health = await self.qwen_client.health_check()
                health_data["components"]["ai"] = ai_health
            
            # Data service health
            if self.data_service:
                data_health = await self.data_service.health_check()
                health_data["components"]["data_collection"] = data_health
            
            # Telegram service health
            if self.telegram_service:
                telegram_health = await self.telegram_service.health_check()
                health_data["components"]["telegram"] = telegram_health
            
            # Content service health
            if self.content_service:
                content_health = await self.content_service.health_check()
                health_data["components"]["content"] = content_health
            
            # Determine overall status
            component_statuses = [
                comp.get("status", "unknown") 
                for comp in health_data["components"].values()
            ]
            
            if any(status == "error" for status in component_statuses):
                health_data["status"] = "error"
            elif any(status == "unhealthy" for status in component_statuses):
                health_data["status"] = "unhealthy"
            
            return health_data
        
        @self.app.get("/metrics")
        async def get_metrics(credentials: HTTPBasicCredentials = Depends(verify_credentials)):
            """Get system metrics"""
            metrics = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "database": {},
                "ai": {},
                "data_collection": {},
                "telegram": {},
                "content": {}
            }
            
            # Database metrics
            if self.db_manager:
                metrics["database"] = await self.db_manager.get_connection_stats()
            
            # AI metrics
            if self.qwen_client:
                metrics["ai"] = await self.qwen_client.get_stats()
            
            # Data collection metrics
            if self.data_service:
                metrics["data_collection"] = self.data_service.get_stats()
            
            # Telegram metrics
            if self.telegram_service:
                metrics["telegram"] = self.telegram_service.get_stats()
            
            # Content metrics
            if self.content_service:
                metrics["content"] = self.content_service.get_stats()
            
            return metrics
        
        @self.app.get("/config")
        async def get_config(credentials: HTTPBasicCredentials = Depends(verify_credentials)):
            """Get system configuration"""
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(select(Config))
                else:
                    from sqlalchemy import select
                    result = await session.execute(select(Config))
                
                config = result.scalars().first()
                
                if config:
                    return config.to_dict()
                else:
                    raise HTTPException(status_code=404, detail="Configuration not found")
        
        @self.app.get("/channels")
        async def get_channels(credentials: HTTPBasicCredentials = Depends(verify_credentials)):
            """Get all channels"""
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(select(Channel))
                else:
                    from sqlalchemy import select
                    result = await session.execute(select(Channel))
                
                channels = result.scalars().all()
                return [channel.to_dict() for channel in channels]
        
        @self.app.post("/data/collect")
        async def trigger_data_collection(credentials: HTTPBasicCredentials = Depends(verify_credentials)):
            """Trigger immediate data collection"""
            if not self.data_service:
                raise HTTPException(status_code=503, detail="Data collection service not available")
            
            try:
                result = await self.data_service.collect_now()
                return {"status": "success", "result": result}
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/ai/test")
        async def test_ai_connection(credentials: HTTPBasicCredentials = Depends(verify_credentials)):
            """Test AI connection"""
            if not self.qwen_client:
                raise HTTPException(status_code=503, detail="AI client not available")
            
            try:
                response = await self.qwen_client.generate_completion(
                    "Test message for MignalyBot v2",
                    max_tokens=50
                )
                return {"status": "success", "response": response}
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/telegram/test/{chat_id}")
        async def test_telegram_message(
            chat_id: str,
            credentials: HTTPBasicCredentials = Depends(verify_credentials)
        ):
            """Send test message to Telegram"""
            if not self.telegram_service:
                raise HTTPException(status_code=503, detail="Telegram service not available")
            
            try:
                message_id = await self.telegram_service.send_message(
                    chat_id,
                    "🤖 Test message from MignalyBot v2 Admin Interface"
                )
                return {"status": "success", "message_id": message_id}
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
    
    async def start(self):
        """Start the admin service"""
        if self.running:
            self.logger.warning("Admin service is already running")
            return
        
        self.running = True
        self.logger.info(f"Starting admin service on {self.settings.admin.host}:{self.settings.admin.port}")
        
        # Start the server
        config = uvicorn.Config(
            self.app,
            host=self.settings.admin.host,
            port=self.settings.admin.port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        
        self.server_task = asyncio.create_task(server.serve())
        
        self.logger.info("Admin service started successfully")
    
    async def stop(self):
        """Stop the admin service"""
        if not self.running:
            return
        
        self.running = False
        self.logger.info("Stopping admin service")
        
        if self.server_task:
            self.server_task.cancel()
            try:
                await self.server_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Admin service stopped")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            if not self.running:
                return {
                    'status': 'stopped',
                    'message': 'Admin service is not running'
                }
            
            return {
                'status': 'healthy',
                'host': self.settings.admin.host,
                'port': self.settings.admin.port
            }
        
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
