"""
Utility functions for cleaning duplicate data
"""

import logging
from datetime import datetime, timezone
from sqlalchemy import select, func, and_, delete, text
from src.database.setup import get_async_db, is_sqlite_db
from src.database.models import EconomicEvent

logger = logging.getLogger(__name__)


async def delete_duplicate_events():
    """
    Delete duplicate economic events based on title, event_time, and country.
    Keeps the first occurrence (oldest created_at) and deletes the rest.
    """
    logger.info("Starting duplicate event cleanup...")
    
    try:
        async for db in get_async_db():
            # Find duplicates using a subquery approach
            if is_sqlite_db():
                # For SQLite, we need to use a different approach
                # First, get all events
                all_events_result = db.execute(
                    select(EconomicEvent).order_by(EconomicEvent.created_at)
                )
                all_events = all_events_result.scalars().all()
                
                # Track seen events and duplicates to delete
                seen_events = set()
                duplicates_to_delete = []
                
                for event in all_events:
                    # Create a unique key based on title, event_time, and country
                    event_key = (
                        event.title.strip().lower() if event.title else "",
                        event.event_time.isoformat() if event.event_time else "",
                        event.country.strip().lower() if event.country else ""
                    )
                    
                    if event_key in seen_events:
                        duplicates_to_delete.append(event.id)
                    else:
                        seen_events.add(event_key)
                
                # Delete duplicates
                deleted_count = 0
                for event_id in duplicates_to_delete:
                    db.execute(delete(EconomicEvent).where(EconomicEvent.id == event_id))
                    deleted_count += 1
                
                if deleted_count > 0:
                    db.commit()
                    logger.info(f"Deleted {deleted_count} duplicate events from SQLite database")
                else:
                    logger.info("No duplicate events found in SQLite database")
                    
            else:
                # For PostgreSQL, use a more efficient approach with window functions
                # First, find duplicate IDs
                duplicate_ids_query = """
                WITH duplicates AS (
                    SELECT id,
                           ROW_NUMBER() OVER (
                               PARTITION BY LOWER(TRIM(title)), event_time, LOWER(TRIM(COALESCE(country, '')))
                               ORDER BY created_at
                           ) as row_num
                    FROM economic_events
                )
                SELECT id FROM duplicates WHERE row_num > 1
                """

                duplicate_ids_result = await db.execute(text(duplicate_ids_query))
                duplicate_ids = [row[0] for row in duplicate_ids_result.fetchall()]

                deleted_count = 0
                if duplicate_ids:
                    # Delete duplicates using SQLAlchemy
                    from sqlalchemy import delete
                    delete_query = delete(EconomicEvent).where(EconomicEvent.id.in_(duplicate_ids))
                    result = await db.execute(delete_query)
                    deleted_count = result.rowcount

                    if deleted_count > 0:
                        await db.commit()
                        logger.info(f"Deleted {deleted_count} duplicate events from PostgreSQL database")
                    else:
                        logger.info("No duplicate events found in PostgreSQL database")
                else:
                    logger.info("No duplicate events found in PostgreSQL database")
            
            return deleted_count if 'deleted_count' in locals() else 0
            
    except Exception as e:
        logger.error(f"Error during duplicate event cleanup: {e}", exc_info=True)
        # Rollback in case of error
        try:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
        except:
            pass
        return 0


async def get_duplicate_events_count():
    """
    Get the count of duplicate events without deleting them.
    Useful for reporting purposes.
    """
    try:
        async for db in get_async_db():
            if is_sqlite_db():
                # For SQLite, count duplicates manually
                all_events_result = db.execute(
                    select(EconomicEvent)
                )
                all_events = all_events_result.scalars().all()
                
                seen_events = set()
                duplicate_count = 0
                
                for event in all_events:
                    event_key = (
                        event.title.strip().lower() if event.title else "",
                        event.event_time.isoformat() if event.event_time else "",
                        event.country.strip().lower() if event.country else ""
                    )
                    
                    if event_key in seen_events:
                        duplicate_count += 1
                    else:
                        seen_events.add(event_key)
                
                return duplicate_count
                
            else:
                # For PostgreSQL, use window functions
                duplicate_count_query = """
                WITH duplicates AS (
                    SELECT id,
                           ROW_NUMBER() OVER (
                               PARTITION BY LOWER(TRIM(title)), event_time, LOWER(TRIM(COALESCE(country, '')))
                               ORDER BY created_at
                           ) as row_num
                    FROM economic_events
                )
                SELECT COUNT(*) FROM duplicates WHERE row_num > 1
                """

                result = await db.execute(text(duplicate_count_query))
                return result.scalar() or 0
                
    except Exception as e:
        logger.error(f"Error counting duplicate events: {e}", exc_info=True)
        return 0


async def cleanup_old_events(days_to_keep: int = 30):
    """
    Delete events older than specified days.
    
    Args:
        days_to_keep: Number of days to keep events (default: 30)
    """
    logger.info(f"Cleaning up events older than {days_to_keep} days...")
    
    try:
        cutoff_date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - days_to_keep)
        
        async for db in get_async_db():
            if is_sqlite_db():
                result = db.execute(
                    delete(EconomicEvent).where(EconomicEvent.created_at < cutoff_date)
                )
                deleted_count = result.rowcount
                db.commit()
            else:
                result = await db.execute(
                    delete(EconomicEvent).where(EconomicEvent.created_at < cutoff_date)
                )
                deleted_count = result.rowcount
                await db.commit()
            
            if deleted_count > 0:
                logger.info(f"Deleted {deleted_count} old events (older than {days_to_keep} days)")
            else:
                logger.info(f"No old events found (older than {days_to_keep} days)")
            
            return deleted_count
            
    except Exception as e:
        logger.error(f"Error during old events cleanup: {e}", exc_info=True)
        try:
            if is_sqlite_db():
                db.rollback()
            else:
                await db.rollback()
        except:
            pass
        return 0
