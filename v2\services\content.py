"""
Content generation service for MignalyBot v2

Features:
- AI-powered content generation
- Multiple content types (news, signals, analysis, events, greetings)
- Language support (English/Farsi)
- Performance optimization
- Content scheduling
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional
import random

from core.config.settings import Settings
from core.utils.helpers import async_performance_monitor
from core.exceptions.base import MignalyBotException
from database.connection.manager import DatabaseManager
from database.models.core import Channel, Post, PostType, PostStatus
from database.models.content import NewsItem, EconomicEvent
from database.models.market_data import TradingSignal, CandleData
from ai.clients.qwen import QwenClient


class ContentService:
    """AI-powered content generation service"""
    
    def __init__(self, settings: Settings, db_manager: DatabaseManager, qwen_client: QwenClient):
        self.settings = settings
        self.db_manager = db_manager
        self.qwen_client = qwen_client
        self.logger = logging.getLogger(__name__)
        
        # Service state
        self.running = False
        self.content_generation_task = None
        
        # Statistics
        self.stats = {
            'total_content_generated': 0,
            'content_by_type': {
                'news': 0,
                'signals': 0,
                'analysis': 0,
                'events': 0,
                'greetings': 0
            },
            'content_by_language': {
                'en': 0,
                'fa': 0
            },
            'last_generation_time': None
        }
    
    async def start(self):
        """Start the content service"""
        if self.running:
            self.logger.warning("Content service is already running")
            return
        
        self.running = True
        self.logger.info("Starting content service")
        
        # Start content generation loop
        self.content_generation_task = asyncio.create_task(self._content_generation_loop())
    
    async def stop(self):
        """Stop the content service"""
        if not self.running:
            return
        
        self.running = False
        self.logger.info("Stopping content service")
        
        if self.content_generation_task:
            self.content_generation_task.cancel()
            try:
                await self.content_generation_task
            except asyncio.CancelledError:
                pass
    
    async def _content_generation_loop(self):
        """Main content generation loop"""
        try:
            while self.running:
                await asyncio.sleep(3600)  # Run every hour
                
                if self.running:
                    await self._generate_scheduled_content()
        
        except asyncio.CancelledError:
            self.logger.info("Content generation loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in content generation loop: {e}")
    
    @async_performance_monitor("generate_scheduled_content")
    async def _generate_scheduled_content(self):
        """Generate scheduled content for all channels"""
        try:
            # Get all active channels
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(
                        select(Channel).where(Channel.active == True)
                    )
                else:
                    from sqlalchemy import select
                    result = await session.execute(
                        select(Channel).where(Channel.active == True)
                    )
                
                channels = result.scalars().all()
                
                for channel in channels:
                    await self._generate_content_for_channel(channel)
        
        except Exception as e:
            self.logger.error(f"Error generating scheduled content: {e}")
    
    async def _generate_content_for_channel(self, channel):
        """Generate content for a specific channel"""
        try:
            # Determine what content to generate based on channel settings
            content_types = []
            
            if channel.enable_news:
                content_types.append(PostType.NEWS)
            if channel.enable_signals:
                content_types.append(PostType.SIGNAL)
            if channel.enable_analysis:
                content_types.append(PostType.ANALYSIS)
            if channel.enable_events:
                content_types.append(PostType.EVENT)
            if channel.enable_greetings:
                content_types.append(PostType.GREETING)
            
            # Generate content for each type
            for content_type in content_types:
                # Check if we should generate this type of content
                if await self._should_generate_content(channel, content_type):
                    await self._generate_content(channel, content_type)
        
        except Exception as e:
            self.logger.error(f"Error generating content for channel {channel.name}: {e}")
    
    async def _should_generate_content(self, channel, content_type: PostType) -> bool:
        """Check if we should generate content of this type for the channel"""
        try:
            # Check recent posts to avoid spam
            now = datetime.now()
            cutoff_time = now - timedelta(hours=channel.post_frequency)
            
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select, func
                    result = session.execute(
                        select(func.count(Post.id)).where(
                            Post.channel_id == channel.id,
                            Post.type == content_type,
                            Post.created_at >= cutoff_time
                        )
                    )
                else:
                    from sqlalchemy import select, func
                    result = await session.execute(
                        select(func.count(Post.id)).where(
                            Post.channel_id == channel.id,
                            Post.type == content_type,
                            Post.created_at >= cutoff_time
                        )
                    )
                
                recent_count = result.scalar()
                
                # Don't generate if we already have recent content of this type
                return recent_count == 0
        
        except Exception as e:
            self.logger.error(f"Error checking content generation need: {e}")
            return False
    
    @async_performance_monitor("generate_content")
    async def _generate_content(self, channel, content_type: PostType):
        """Generate content of specified type"""
        try:
            content = None
            
            if content_type == PostType.NEWS:
                content = await self._generate_news_content(channel)
            elif content_type == PostType.SIGNAL:
                content = await self._generate_signal_content(channel)
            elif content_type == PostType.ANALYSIS:
                content = await self._generate_analysis_content(channel)
            elif content_type == PostType.EVENT:
                content = await self._generate_event_content(channel)
            elif content_type == PostType.GREETING:
                content = await self._generate_greeting_content(channel)
            
            if content:
                # Create post
                await self._create_post(channel, content_type, content)
                
                # Update statistics
                self.stats['total_content_generated'] += 1
                self.stats['content_by_type'][content_type.value] += 1
                self.stats['content_by_language'][channel.language] += 1
                self.stats['last_generation_time'] = datetime.now()
                
                self.logger.info(f"Generated {content_type.value} content for channel {channel.name}")
        
        except Exception as e:
            self.logger.error(f"Error generating {content_type.value} content: {e}")
    
    async def _generate_news_content(self, channel) -> Optional[str]:
        """Generate news analysis content"""
        try:
            # Get recent news items
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(
                        select(NewsItem).where(
                            NewsItem.processed == False
                        ).limit(1)
                    )
                else:
                    from sqlalchemy import select
                    result = await session.execute(
                        select(NewsItem).where(
                            NewsItem.processed == False
                        ).limit(1)
                    )
                
                news_item = result.scalars().first()
                
                if news_item:
                    # Generate AI analysis
                    symbols = news_item.symbols.split(',') if news_item.symbols else ['BTC/USD', 'EUR/USD']
                    
                    content = await self.qwen_client.generate_news_analysis(
                        news_item.title,
                        news_item.content or news_item.title,
                        symbols,
                        channel.language
                    )
                    
                    # Mark as processed
                    news_item.processed = True
                    news_item.ai_analysis = content
                    
                    if self.db_manager.settings.database.is_sqlite:
                        session.commit()
                    else:
                        await session.commit()
                    
                    return content
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error generating news content: {e}")
            return None
    
    async def _generate_signal_content(self, channel) -> Optional[str]:
        """Generate trading signal content"""
        try:
            # Get recent trading signals
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(
                        select(TradingSignal).where(
                            TradingSignal.status == "active"
                        ).limit(1)
                    )
                else:
                    from sqlalchemy import select
                    result = await session.execute(
                        select(TradingSignal).where(
                            TradingSignal.status == "active"
                        ).limit(1)
                    )
                
                signal = result.scalars().first()
                
                if signal:
                    # Generate signal content
                    if channel.language == "fa":
                        content = f"""
🚨 *سیگنال معاملاتی جدید*

📊 نماد: {signal.symbol}
⏰ تایم فریم: {signal.timeframe}
📈 جهت: {'خرید' if signal.direction == 'buy' else 'فروش'}

💰 قیمت ورود: {signal.entry_price}
🛑 حد ضرر: {signal.stop_loss}
🎯 حد سود: {signal.take_profit}

📊 اعتماد: {signal.confidence * 100:.0f}%
⚡ قدرت سیگنال: {signal.signal_strength * 100:.0f}%

{signal.ai_analysis or 'تحلیل AI در دسترس نیست'}
                        """
                    else:
                        content = f"""
🚨 *New Trading Signal*

📊 Symbol: {signal.symbol}
⏰ Timeframe: {signal.timeframe}
📈 Direction: {signal.direction.upper()}

💰 Entry Price: {signal.entry_price}
🛑 Stop Loss: {signal.stop_loss}
🎯 Take Profit: {signal.take_profit}

📊 Confidence: {signal.confidence * 100:.0f}%
⚡ Signal Strength: {signal.signal_strength * 100:.0f}%

{signal.ai_analysis or 'AI analysis not available'}
                        """
                    
                    return content.strip()
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error generating signal content: {e}")
            return None
    
    async def _generate_analysis_content(self, channel) -> Optional[str]:
        """Generate market analysis content"""
        try:
            # Get recent candle data for analysis
            symbols = ['BTC/USD', 'EUR/USD', 'ETH/USD']
            symbol = random.choice(symbols)
            timeframe = random.choice(['1h', '4h', '1d'])
            
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(
                        select(CandleData).where(
                            CandleData.symbol == symbol,
                            CandleData.timeframe == timeframe
                        ).order_by(CandleData.timestamp.desc()).limit(20)
                    )
                else:
                    from sqlalchemy import select
                    result = await session.execute(
                        select(CandleData).where(
                            CandleData.symbol == symbol,
                            CandleData.timeframe == timeframe
                        ).order_by(CandleData.timestamp.desc()).limit(20)
                    )
                
                candles = result.scalars().all()
                
                if candles:
                    content = await self.qwen_client.generate_market_analysis(
                        symbol,
                        timeframe,
                        candles,
                        channel.language,
                        channel.brand_name
                    )
                    return content
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error generating analysis content: {e}")
            return None
    
    async def _generate_event_content(self, channel) -> Optional[str]:
        """Generate economic event content"""
        try:
            # Get upcoming events
            now = datetime.now(timezone.utc)

            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(
                        select(EconomicEvent).where(
                            EconomicEvent.event_time > now,
                            EconomicEvent.processed == False
                        ).limit(1)
                    )
                else:
                    from sqlalchemy import select
                    result = await session.execute(
                        select(EconomicEvent).where(
                            EconomicEvent.event_time > now,
                            EconomicEvent.processed == False
                        ).limit(1)
                    )
                
                event = result.scalars().first()
                
                if event:
                    if channel.language == "fa":
                        content = f"""
📅 *رویداد اقتصادی مهم*

📊 عنوان: {event.title}
🌍 کشور: {event.country}
💱 ارز: {event.currency}
⏰ زمان: {event.event_time.strftime('%Y-%m-%d %H:%M')}

📈 تأثیر: {'بالا' if event.impact == 3 else 'متوسط' if event.impact == 2 else 'پایین'}

{event.description or 'توضیحات در دسترس نیست'}
                        """
                    else:
                        content = f"""
📅 *Important Economic Event*

📊 Title: {event.title}
🌍 Country: {event.country}
💱 Currency: {event.currency}
⏰ Time: {event.event_time.strftime('%Y-%m-%d %H:%M')}

📈 Impact: {'High' if event.impact == 3 else 'Medium' if event.impact == 2 else 'Low'}

{event.description or 'No description available'}
                        """
                    
                    # Mark as processed
                    event.processed = True
                    
                    if self.db_manager.settings.database.is_sqlite:
                        session.commit()
                    else:
                        await session.commit()
                    
                    return content.strip()
            
            return None
        
        except Exception as e:
            self.logger.error(f"Error generating event content: {e}")
            return None
    
    async def _generate_greeting_content(self, channel) -> Optional[str]:
        """Generate daily greeting content"""
        try:
            if channel.language == "fa":
                content = f"""
🌅 *صبح بخیر {channel.brand_name or 'عزیزان'}!*

🤖 MignalyBot v2 آماده ارائه بهترین تحلیل‌های بازار مالی است!

✨ *امکانات جدید v2:*
• استراتژی‌های معاملاتی هوش مصنوعی
• عملکرد 75% سریع‌تر
• تحلیل‌های پیشرفته بازار
• پشتیبانی بهبود یافته

📊 امروز منتظر تحلیل‌های جامع و سیگنال‌های دقیق باشید!

💪 موفق باشید!
                """
            else:
                content = f"""
🌅 *Good Morning {channel.brand_name or 'Traders'}!*

🤖 MignalyBot v2 is ready to provide the best financial market analysis!

✨ *New v2 Features:*
• AI-generated trading strategies
• 75% faster performance
• Advanced market analysis
• Enhanced support

📊 Expect comprehensive analysis and accurate signals today!

💪 Happy Trading!
                """
            
            return content.strip()
        
        except Exception as e:
            self.logger.error(f"Error generating greeting content: {e}")
            return None
    
    async def _create_post(self, channel, content_type: PostType, content: str):
        """Create a new post"""
        try:
            async with self.db_manager.get_async_session() as session:
                post = Post(
                    channel_id=channel.id,
                    type=content_type,
                    content=content,
                    status=PostStatus.SCHEDULED,
                    scheduled_time=datetime.now(timezone.utc) + timedelta(minutes=random.randint(1, 30)),
                    language=channel.language,
                    ai_generated=True
                )
                
                session.add(post)
                
                if self.db_manager.settings.database.is_sqlite:
                    session.commit()
                else:
                    await session.commit()
        
        except Exception as e:
            self.logger.error(f"Error creating post: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        return self.stats.copy()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            if not self.running:
                return {
                    'status': 'stopped',
                    'message': 'Content service is not running'
                }
            
            return {
                'status': 'healthy',
                'stats': self.get_stats()
            }
        
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
