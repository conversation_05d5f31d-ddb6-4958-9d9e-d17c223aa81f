/* MignalyBot Catalog - Responsive CSS */

/* RTL (Right-to-Left) Support for Farsi */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .hero-content {
    grid-template-columns: 1fr 1fr;
}

[dir="rtl"] .hero-text {
    order: 2;
}

[dir="rtl"] .hero-image {
    order: 1;
}

[dir="rtl"] .nav-menu {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-brand {
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-stats {
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-actions {
    flex-direction: row-reverse;
}

[dir="rtl"] .benefit-item {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .contact-method {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .testimonial-author {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .footer-content {
    text-align: right;
}

[dir="rtl"] .plan-features li {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .faq-question::after {
    left: 1.5rem;
    right: auto;
}

/* Language Toggle RTL */
[dir="rtl"] .language-toggle {
    left: 20px;
    right: auto;
}

/* Navigation RTL adjustments */
[dir="rtl"] .nav-link::after {
    right: 0;
    left: auto;
}

/* Form RTL adjustments */
[dir="rtl"] .form-group input,
[dir="rtl"] .form-group select,
[dir="rtl"] .form-group textarea {
    text-align: right;
}

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 16px;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-content {
        gap: 3rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 800px;
    }
    
    .tech-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    /* Navigation */
    .navbar .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .nav-actions {
        width: 100%;
        justify-content: center;
    }
    
    /* Hero Section */
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .hero-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    /* RTL Mobile adjustments */
    [dir="rtl"] .hero-content {
        text-align: center;
    }
    
    [dir="rtl"] .hero-stats {
        justify-content: center;
    }
    
    [dir="rtl"] .hero-actions {
        align-items: center;
    }
    
    /* Sections */
    .section-header {
        margin-bottom: 3rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    /* Benefits */
    .benefits-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .benefit-item {
        flex-direction: row;
        text-align: left;
    }
    
    [dir="rtl"] .benefit-item {
        flex-direction: row-reverse;
        text-align: right;
    }
    
    /* Pricing */
    .pricing-grid {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
    
    .pricing-card.featured {
        transform: none;
        order: -1;
    }
    
    .pricing-toggle {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    /* Technical */
    .tech-grid {
        grid-template-columns: 1fr;
    }
    
    .tech-card {
        padding: 1.5rem;
    }
    
    /* FAQ */
    .faq-question {
        padding: 1rem;
        font-size: 1rem;
    }
    
    .faq-answer {
        padding: 0 1rem;
    }
    
    .faq-item.active .faq-answer {
        padding: 1rem;
    }
    
    /* Testimonials */
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    /* Contact */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contact-methods {
        margin-top: 1rem;
    }
    
    .contact-method {
        flex-direction: row;
        text-align: left;
    }
    
    [dir="rtl"] .contact-method {
        flex-direction: row-reverse;
        text-align: right;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    [dir="rtl"] .footer-content {
        text-align: center;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 12px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .feature-card,
    .pricing-card,
    .tech-card,
    .testimonial-card,
    .contact-form {
        padding: 1rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .btn-large {
        padding: 14px 28px;
        font-size: 1rem;
    }
    
    .hero-stats {
        gap: 0.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .price {
        font-size: 2.5rem;
    }
    
    .language-toggle {
        top: 10px;
        right: 10px;
    }
    
    [dir="rtl"] .language-toggle {
        left: 10px;
        right: auto;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .language-toggle,
    .hero-actions,
    .contact-form,
    .footer {
        display: none;
    }
    
    .hero {
        padding: 20px 0;
    }
    
    .section-header {
        margin-bottom: 20px;
    }
    
    .pricing-card,
    .feature-card,
    .tech-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1 { font-size: 24pt; }
    h2 { font-size: 20pt; }
    h3 { font-size: 16pt; }
    h4 { font-size: 14pt; }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
        --bg-card: #ffffff;
        --bg-secondary: #f0f0f0;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}
