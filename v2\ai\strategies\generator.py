"""
AI-powered trading strategy generator for MignalyBot v2

Features:
- Dynamic strategy generation based on market conditions
- Multiple strategy types (trend following, mean reversion, etc.)
- Language-specific strategy descriptions
- Performance optimization
- Market condition analysis
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

from ai.clients.qwen import QwenClient
from core.utils.helpers import async_performance_monitor
from core.exceptions.base import AIAPIException


class StrategyType(Enum):
    """Trading strategy types"""
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    VOLATILITY_BREAKOUT = "volatility_breakout"
    SCALPING = "scalping"
    SWING_TRADING = "swing_trading"


class MarketCondition(Enum):
    """Market condition types"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    UNCERTAIN = "uncertain"


class StrategyGenerator:
    """AI-powered trading strategy generator"""
    
    def __init__(self, qwen_client: QwenClient):
        self.qwen_client = qwen_client
        self.logger = logging.getLogger(__name__)
        
        # Strategy templates for different types
        self.strategy_templates = {
            StrategyType.TREND_FOLLOWING: self._get_trend_following_template(),
            StrategyType.MEAN_REVERSION: self._get_mean_reversion_template(),
            StrategyType.MOMENTUM: self._get_momentum_template(),
            StrategyType.VOLATILITY_BREAKOUT: self._get_volatility_breakout_template(),
            StrategyType.SCALPING: self._get_scalping_template(),
            StrategyType.SWING_TRADING: self._get_swing_trading_template()
        }
    
    def _get_trend_following_template(self) -> Dict[str, Any]:
        """Get trend following strategy template"""
        return {
            "description": "A trend following strategy that identifies and follows market trends using moving averages and momentum indicators",
            "indicators": ["EMA", "MACD", "ADX", "ATR"],
            "timeframes": ["4h", "1d"],
            "risk_management": {
                "stop_loss_atr_multiplier": 2.0,
                "take_profit_ratio": 2.0,
                "position_size": 0.02
            },
            "entry_conditions": [
                "Fast EMA crosses above Slow EMA",
                "MACD line crosses above signal line",
                "ADX > 25 (strong trend)"
            ],
            "exit_conditions": [
                "Fast EMA crosses below Slow EMA",
                "Stop loss or take profit hit",
                "ADX < 20 (weak trend)"
            ]
        }
    
    def _get_mean_reversion_template(self) -> Dict[str, Any]:
        """Get mean reversion strategy template"""
        return {
            "description": "A mean reversion strategy that trades when price deviates significantly from its average",
            "indicators": ["RSI", "Bollinger Bands", "SMA", "ATR"],
            "timeframes": ["1h", "4h"],
            "risk_management": {
                "stop_loss_atr_multiplier": 1.5,
                "take_profit_ratio": 1.5,
                "position_size": 0.015
            },
            "entry_conditions": [
                "RSI < 30 (oversold) for long",
                "RSI > 70 (overbought) for short",
                "Price touches Bollinger Band"
            ],
            "exit_conditions": [
                "RSI returns to 50",
                "Price reaches opposite Bollinger Band",
                "Stop loss or take profit hit"
            ]
        }
    
    def _get_momentum_template(self) -> Dict[str, Any]:
        """Get momentum strategy template"""
        return {
            "description": "A momentum strategy that captures strong price movements in trending markets",
            "indicators": ["RSI", "MACD", "Volume", "ATR"],
            "timeframes": ["1h", "4h"],
            "risk_management": {
                "stop_loss_atr_multiplier": 2.5,
                "take_profit_ratio": 3.0,
                "position_size": 0.025
            },
            "entry_conditions": [
                "Strong price breakout with volume",
                "RSI > 60 for long, RSI < 40 for short",
                "MACD histogram increasing"
            ],
            "exit_conditions": [
                "Momentum weakening",
                "Volume declining",
                "Stop loss or take profit hit"
            ]
        }
    
    def _get_volatility_breakout_template(self) -> Dict[str, Any]:
        """Get volatility breakout strategy template"""
        return {
            "description": "A volatility breakout strategy that trades breakouts from consolidation periods",
            "indicators": ["ATR", "Bollinger Bands", "Volume", "ADX"],
            "timeframes": ["1h", "4h", "1d"],
            "risk_management": {
                "stop_loss_atr_multiplier": 1.8,
                "take_profit_ratio": 2.5,
                "position_size": 0.02
            },
            "entry_conditions": [
                "Low volatility period (ATR declining)",
                "Price breaks out of consolidation",
                "Volume spike on breakout"
            ],
            "exit_conditions": [
                "Volatility returns to normal",
                "False breakout detected",
                "Stop loss or take profit hit"
            ]
        }
    
    def _get_scalping_template(self) -> Dict[str, Any]:
        """Get scalping strategy template"""
        return {
            "description": "A scalping strategy for quick profits in short timeframes",
            "indicators": ["EMA", "RSI", "MACD", "Volume"],
            "timeframes": ["5m", "15m"],
            "risk_management": {
                "stop_loss_atr_multiplier": 1.0,
                "take_profit_ratio": 1.2,
                "position_size": 0.01
            },
            "entry_conditions": [
                "Quick EMA crossover",
                "RSI momentum shift",
                "High volume confirmation"
            ],
            "exit_conditions": [
                "Quick profit target",
                "Tight stop loss",
                "Time-based exit"
            ]
        }
    
    def _get_swing_trading_template(self) -> Dict[str, Any]:
        """Get swing trading strategy template"""
        return {
            "description": "A swing trading strategy for medium-term position holding",
            "indicators": ["SMA", "RSI", "MACD", "Support/Resistance"],
            "timeframes": ["4h", "1d"],
            "risk_management": {
                "stop_loss_atr_multiplier": 3.0,
                "take_profit_ratio": 2.0,
                "position_size": 0.03
            },
            "entry_conditions": [
                "Price bounces from support/resistance",
                "RSI divergence",
                "MACD confirmation"
            ],
            "exit_conditions": [
                "Opposite support/resistance level",
                "Trend reversal signals",
                "Stop loss or take profit hit"
            ]
        }
    
    def _analyze_market_conditions(self, candles: List[Any]) -> MarketCondition:
        """Analyze current market conditions from candle data"""
        if not candles or len(candles) < 20:
            return MarketCondition.UNCERTAIN
        
        # Calculate basic metrics
        closes = [float(candle.close) for candle in candles[-20:]]
        highs = [float(candle.high) for candle in candles[-20:]]
        lows = [float(candle.low) for candle in candles[-20:]]
        
        # Calculate trend
        sma_short = sum(closes[-10:]) / 10
        sma_long = sum(closes) / 20
        
        # Calculate volatility
        price_changes = [abs(closes[i] - closes[i-1]) / closes[i-1] for i in range(1, len(closes))]
        avg_volatility = sum(price_changes) / len(price_changes)
        
        # Determine market condition
        if sma_short > sma_long * 1.02:  # 2% above
            return MarketCondition.TRENDING_UP
        elif sma_short < sma_long * 0.98:  # 2% below
            return MarketCondition.TRENDING_DOWN
        elif avg_volatility > 0.03:  # 3% average change
            return MarketCondition.HIGH_VOLATILITY
        elif avg_volatility < 0.01:  # 1% average change
            return MarketCondition.LOW_VOLATILITY
        else:
            return MarketCondition.RANGING
    
    def _select_optimal_strategy_type(self, market_condition: MarketCondition) -> StrategyType:
        """Select optimal strategy type based on market conditions"""
        strategy_mapping = {
            MarketCondition.TRENDING_UP: StrategyType.TREND_FOLLOWING,
            MarketCondition.TRENDING_DOWN: StrategyType.TREND_FOLLOWING,
            MarketCondition.RANGING: StrategyType.MEAN_REVERSION,
            MarketCondition.HIGH_VOLATILITY: StrategyType.VOLATILITY_BREAKOUT,
            MarketCondition.LOW_VOLATILITY: StrategyType.SCALPING,
            MarketCondition.UNCERTAIN: StrategyType.SWING_TRADING
        }
        
        return strategy_mapping.get(market_condition, StrategyType.TREND_FOLLOWING)
    
    @async_performance_monitor("generate_strategy")
    async def generate_strategy(
        self,
        symbol: str,
        timeframes: List[str],
        market_data: Optional[List[Any]] = None,
        strategy_type: Optional[StrategyType] = None,
        language: str = "en"
    ) -> Dict[str, Any]:
        """
        Generate AI-powered trading strategy
        
        Args:
            symbol: Trading symbol
            timeframes: List of timeframes
            market_data: Optional market data for analysis
            strategy_type: Optional specific strategy type
            language: Strategy language
            
        Returns:
            Generated strategy dictionary
        """
        try:
            # Analyze market conditions if data provided
            market_condition = MarketCondition.UNCERTAIN
            if market_data:
                market_condition = self._analyze_market_conditions(market_data)
            
            # Select strategy type if not provided
            if not strategy_type:
                strategy_type = self._select_optimal_strategy_type(market_condition)
            
            # Get strategy template
            template = self.strategy_templates[strategy_type]
            
            # Generate AI-enhanced strategy
            strategy_prompt = self._build_strategy_prompt(
                symbol, timeframes, strategy_type, template, market_condition, language
            )
            
            # Get AI-generated strategy code
            strategy_code = await self._generate_strategy_code(
                strategy_type, template, symbol, timeframes, language
            )
            
            # Build complete strategy
            strategy = {
                "name": f"AI {strategy_type.value.replace('_', ' ').title()} - {symbol}",
                "description": await self.qwen_client.generate_completion(strategy_prompt),
                "strategy_type": strategy_type.value,
                "symbols": symbol,
                "timeframes": ",".join(timeframes),
                "parameters": template["risk_management"],
                "code": strategy_code,
                "ai_generated": True,
                "ai_prompt": strategy_prompt,
                "market_conditions": {
                    "condition": market_condition.value,
                    "analysis_time": datetime.utcnow().isoformat(),
                    "recommended_timeframes": template["timeframes"]
                }
            }
            
            return strategy
            
        except Exception as e:
            self.logger.error(f"Error generating strategy: {e}")
            raise AIAPIException(
                f"Failed to generate strategy for {symbol}",
                context={
                    'symbol': symbol,
                    'strategy_type': strategy_type.value if strategy_type else None,
                    'market_condition': market_condition.value if market_condition else None
                },
                original_exception=e
            )
    
    def _build_strategy_prompt(
        self,
        symbol: str,
        timeframes: List[str],
        strategy_type: StrategyType,
        template: Dict[str, Any],
        market_condition: MarketCondition,
        language: str
    ) -> str:
        """Build AI prompt for strategy description"""
        if language == "fa":
            return f"""لطفاً توضیح کاملی از استراتژی معاملاتی {strategy_type.value.replace('_', ' ')} برای {symbol} ارائه دهید.

نوع استراتژی: {strategy_type.value}
نماد: {symbol}
تایم فریم‌ها: {', '.join(timeframes)}
شرایط بازار فعلی: {market_condition.value}

اندیکاتورهای استفاده شده: {', '.join(template['indicators'])}

لطفاً شامل موارد زیر باشد:
1. توضیح کلی استراتژی
2. شرایط ورود به معامله
3. شرایط خروج از معامله
4. مدیریت ریسک
5. نکات مهم برای اجرای موفق

پاسخ را به زبان فارسی و با جزئیات کامل ارائه دهید."""
        else:
            return f"""Please provide a comprehensive description of the {strategy_type.value.replace('_', ' ')} trading strategy for {symbol}.

Strategy Type: {strategy_type.value}
Symbol: {symbol}
Timeframes: {', '.join(timeframes)}
Current Market Condition: {market_condition.value}

Indicators Used: {', '.join(template['indicators'])}

Please include:
1. Strategy overview
2. Entry conditions
3. Exit conditions
4. Risk management
5. Important implementation notes

Provide a detailed response in English."""
    
    async def _generate_strategy_code(
        self,
        strategy_type: StrategyType,
        template: Dict[str, Any],
        symbol: str,
        timeframes: List[str],
        language: str
    ) -> str:
        """Generate Python code for the trading strategy"""
        code_prompt = f"""Generate Python code for a {strategy_type.value.replace('_', ' ')} trading strategy.

Requirements:
- Function name: generate_signals(df, symbol, timeframe, parameters)
- Use pandas and pandas_ta for technical analysis
- Return list of signal dictionaries
- Include proper error handling
- Use indicators: {', '.join(template['indicators'])}
- Risk management parameters: {json.dumps(template['risk_management'], indent=2)}

Signal dictionary format:
{{
    "direction": "buy" or "sell",
    "entry_price": float,
    "stop_loss": float,
    "take_profit": float,
    "confidence": float (0-1),
    "signal_time": datetime,
    "signal_strength": float (0-1)
}}

Generate clean, efficient Python code with comments."""
        
        return await self.qwen_client.generate_completion(
            code_prompt,
            temperature=0.3,  # Lower temperature for more consistent code
            max_tokens=2000
        )
