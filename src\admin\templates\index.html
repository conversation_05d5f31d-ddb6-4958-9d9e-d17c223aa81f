{% extends "base.html" %}

{% block title %}Dashboard - MignalyBot Admin{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="row">
    <!-- Stats Cards -->
    <div class="col-md-3">
        <div class="stat-card primary">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-value" id="activeChannels">-</div>
                    <div class="stat-label">Active Channels</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-broadcast-tower"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stat-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-value" id="activeStrategies">-</div>
                    <div class="stat-label">Active Strategies</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stat-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-value" id="scheduledPosts">-</div>
                    <div class="stat-label">Scheduled Posts</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stat-card danger">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-value" id="activeSignals">-</div>
                    <div class="stat-label">Active Signals</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-signal"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Collection Status -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-database"></i> Data Collection Status</span>
                <button class="btn btn-sm btn-outline-primary" onclick="triggerDataCollection()">
                    <i class="fas fa-sync-alt"></i> Collect Now
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-clock fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">Next Collection In:</h5>
                                <h3 class="mb-0 text-primary" id="dataCollectionCountdown">--:--:--</h3>
                                <small class="text-muted">Runs every 6 hours</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-history fa-2x text-success"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">Last Collection:</h5>
                                <p class="mb-0" id="lastCollectionTime">Loading...</p>
                                <small class="text-muted">Market data, news, events</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Recent Posts -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>Recent Posts</span>
                <a href="/posts" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody id="recentPostsTable">
                            <tr>
                                <td colspan="4" class="text-center">Loading...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Post Status -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">Post Status</div>
            <div class="card-body">
                <canvas id="postStatusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Signal Status -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">Signal Status</div>
            <div class="card-body">
                <canvas id="signalStatusChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">Quick Actions</div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <button class="btn btn-primary" id="collectDataBtn">
                        <i class="fas fa-sync-alt"></i> Collect Market Data
                    </button>
                    <button class="btn btn-success" id="generateContentBtn2">
                        <i class="fas fa-magic"></i> Generate Content (Original)
                    </button>
                    <button class="btn btn-warning" id="generateTemplateContentBtn">
                        <i class="fas fa-bolt"></i> Generate Content (Template - Reduced API)
                    </button>
                    <button class="btn btn-secondary" id="generateGreetingBtn">
                        <i class="fas fa-sun"></i> Generate Greeting Messages
                    </button>
                    <button class="btn btn-info" id="testQwenBtn">
                        <i class="fas fa-robot"></i> Test Qwen API
                    </button>
                    <hr>
                    <div class="text-muted mb-2"><small>Maintenance Actions</small></div>
                    <button class="btn btn-danger" id="removeAllPostsBtn">
                        <i class="fas fa-trash-alt"></i> Remove All Posts
                    </button>
                    <button class="btn btn-danger" id="removeAllSignalsBtn">
                        <i class="fas fa-signal"></i> Remove All Signals
                    </button>
                    <button class="btn btn-warning" id="forceGreetingBtn">
                        <i class="fas fa-sun"></i> Force Greeting Message
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Qwen Modal -->
<div class="modal fade" id="testQwenModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Qwen API</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="qwenPrompt" class="form-label">Enter a prompt:</label>
                    <textarea class="form-control" id="qwenPrompt" rows="4" placeholder="Enter your prompt here..."></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Response:</label>
                    <div class="border rounded p-3 bg-light" id="qwenResponse" style="min-height: 200px; max-height: 400px; overflow-y: auto;">
                        <em>Response will appear here...</em>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="sendQwenPromptBtn">Send Prompt</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    $(document).ready(function() {
        // Load dashboard stats
        loadDashboardStats();

        // Set up event handlers
        $('#collectDataBtn').click(function() {
            $.ajax({
                url: '/api/actions/collect-data',
                type: 'POST',
                success: function(response) {
                    alert('Data collection started successfully!');
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.detail);
                }
            });
        });

        $('#generateContentBtn2').click(function() {
            $.ajax({
                url: '/api/actions/generate-content',
                type: 'POST',
                success: function(response) {
                    alert('Content generation started successfully!');
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.detail);
                }
            });
        });

        $('#generateTemplateContentBtn').click(function() {
            $.ajax({
                url: '/api/actions/generate-template-content',
                type: 'POST',
                success: function(response) {
                    alert('Template-based content generation started successfully! This uses fewer API tokens.');
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.detail);
                }
            });
        });

        $('#generateGreetingBtn').click(function() {
            $.ajax({
                url: '/api/actions/generate-greeting',
                type: 'POST',
                success: function(response) {
                    alert('Greeting message generation started successfully! Greeting posts will be scheduled for all active channels.');
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.detail);
                }
            });
        });

        $('#testQwenBtn').click(function() {
            $('#testQwenModal').modal('show');
        });

        // New maintenance action handlers
        $('#removeAllPostsBtn').click(function() {
            if (confirm('Are you sure you want to remove ALL posts? This action cannot be undone!')) {
                $.ajax({
                    url: '/api/actions/remove-all-posts',
                    type: 'POST',
                    success: function(response) {
                        alert('All posts removed successfully!');
                        loadDashboardStats(); // Refresh stats
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.detail);
                    }
                });
            }
        });

        $('#removeAllSignalsBtn').click(function() {
            if (confirm('Are you sure you want to remove ALL signals? This action cannot be undone!')) {
                $.ajax({
                    url: '/api/actions/remove-all-signals',
                    type: 'POST',
                    success: function(response) {
                        alert('All signals removed successfully!');
                        loadDashboardStats(); // Refresh stats
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.detail);
                    }
                });
            }
        });

        $('#forceGreetingBtn').click(function() {
            if (confirm('Force greeting message generation for all channels? This ignores the daily limit.')) {
                $.ajax({
                    url: '/api/actions/force-greeting',
                    type: 'POST',
                    success: function(response) {
                        alert('Greeting messages forced successfully! Check posts page for scheduled greetings.');
                        loadDashboardStats(); // Refresh stats
                    },
                    error: function(xhr) {
                        alert('Error: ' + xhr.responseJSON.detail);
                    }
                });
            }
        });

        $('#sendQwenPromptBtn').click(function() {
            const prompt = $('#qwenPrompt').val();
            if (!prompt) {
                alert('Please enter a prompt');
                return;
            }

            $('#qwenResponse').html('<div class="text-center"><div class="spinner-border text-primary" role="status"></div><div>Generating response...</div></div>');

            $.ajax({
                url: '/api/actions/test-qwen',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ prompt: prompt }),
                success: function(response) {
                    $('#qwenResponse').html(response.response.replace(/\n/g, '<br>'));
                },
                error: function(xhr) {
                    $('#qwenResponse').html('<div class="text-danger">Error: ' + xhr.responseJSON.detail + '</div>');
                }
            });
        });

        // Refresh stats every 30 seconds
        setInterval(loadDashboardStats, 30000);
    });

    function loadDashboardStats() {
        $.ajax({
            url: '/api/stats',
            type: 'GET',
            success: function(data) {
                // Update stat cards
                $('#activeChannels').text(data.channels.active);
                $('#activeStrategies').text(data.strategies.active);
                $('#scheduledPosts').text(data.posts.scheduled || 0);
                $('#activeSignals').text(data.signals.active || 0);

                // Update recent posts table
                let postsHtml = '';
                if (data.recent_posts && data.recent_posts.length > 0) {
                    data.recent_posts.forEach(function(post) {
                        postsHtml += `
                            <tr>
                                <td>${post.id}</td>
                                <td><span class="badge bg-primary">${post.type}</span></td>
                                <td><span class="badge bg-${getStatusColor(post.status)}">${post.status}</span></td>
                                <td>${formatDate(post.created_at)}</td>
                            </tr>
                        `;
                    });
                } else {
                    postsHtml = '<tr><td colspan="4" class="text-center">No posts found</td></tr>';
                }
                $('#recentPostsTable').html(postsHtml);

                // Update post status chart
                updatePostStatusChart(data.posts);

                // Update signal status chart
                updateSignalStatusChart(data.signals);
            },
            error: function(xhr) {
                console.error('Error loading dashboard stats:', xhr);
            }
        });
    }

    let postStatusChart = null;
    function updatePostStatusChart(postData) {
        const ctx = document.getElementById('postStatusChart').getContext('2d');

        const labels = Object.keys(postData);
        const values = Object.values(postData);

        if (postStatusChart) {
            postStatusChart.data.labels = labels;
            postStatusChart.data.datasets[0].data = values;
            postStatusChart.update();
        } else {
            postStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: [
                            '#3498db',  // draft
                            '#f39c12',  // scheduled
                            '#2ecc71',  // published
                            '#e74c3c'   // failed
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }
    }

    let signalStatusChart = null;
    function updateSignalStatusChart(signalData) {
        const ctx = document.getElementById('signalStatusChart').getContext('2d');

        const labels = Object.keys(signalData);
        const values = Object.values(signalData);

        if (signalStatusChart) {
            signalStatusChart.data.labels = labels;
            signalStatusChart.data.datasets[0].data = values;
            signalStatusChart.update();
        } else {
            signalStatusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: [
                            '#3498db',  // active
                            '#2ecc71',  // tp_hit
                            '#e74c3c',  // sl_hit
                            '#95a5a6',  // expired
                            '#9b59b6'   // cancelled
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }
    }

    function getStatusColor(status) {
        switch (status) {
            case 'draft': return 'primary';
            case 'scheduled': return 'warning';
            case 'published': return 'success';
            case 'failed': return 'danger';
            default: return 'secondary';
        }
    }

    function formatDate(dateString) {
        if (!dateString) return '-';

        try {
            // Parse the date string (stored in UTC)
            const date = new Date(dateString);

            // Check if the date is valid
            if (isNaN(date.getTime())) {
                console.error('Invalid date:', dateString);
                return dateString;
            }

            // Convert UTC to Tehran timezone for display
            // Tehran is UTC+3:30 (3.5 hours ahead of UTC)
            const tehranOffset = 3.5 * 60 * 60 * 1000; // 3.5 hours in milliseconds
            const tehranTime = new Date(date.getTime() + tehranOffset);

            // Format the date with timezone info
            const formattedDate = tehranTime.toLocaleString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });

            return formattedDate + ' (Asia/Tehran)';
        } catch (e) {
            console.error('Error formatting date:', e);
            return dateString;
        }
    }

    // Alias for compatibility
    function formatDateWithTimezone(dateString) {
        return formatDate(dateString);
    }

    // Data Collection Countdown
    let countdownInterval = null;
    let nextCollectionSeconds = 0;

    function updateDataCollectionStatus() {
        fetch('/api/data-collection-status')
            .then(response => response.json())
            .then(data => {
                // Update last collection time
                if (data.last_collection) {
                    document.getElementById('lastCollectionTime').textContent = formatDateWithTimezone(data.last_collection);
                } else {
                    document.getElementById('lastCollectionTime').textContent = 'No data available';
                }

                // Update countdown
                nextCollectionSeconds = data.next_collection_in_seconds;
                updateCountdownDisplay();

                // Start countdown timer if not already running
                if (!countdownInterval) {
                    countdownInterval = setInterval(updateCountdownDisplay, 1000);
                }
            })
            .catch(error => {
                console.error('Error fetching data collection status:', error);
                document.getElementById('dataCollectionCountdown').textContent = 'Error';
                document.getElementById('lastCollectionTime').textContent = 'Error loading';
            });
    }

    function updateCountdownDisplay() {
        if (nextCollectionSeconds <= 0) {
            document.getElementById('dataCollectionCountdown').textContent = '00:00:00';
            // Refresh status when countdown reaches zero
            updateDataCollectionStatus();
            return;
        }

        const hours = Math.floor(nextCollectionSeconds / 3600);
        const minutes = Math.floor((nextCollectionSeconds % 3600) / 60);
        const seconds = nextCollectionSeconds % 60;

        const formatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('dataCollectionCountdown').textContent = formatted;

        nextCollectionSeconds--;
    }

    function triggerDataCollection() {
        if (confirm('Are you sure you want to trigger data collection now?')) {
            fetch('/api/actions/collect-data', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('Data collection started successfully!');
                    // Refresh status after a short delay
                    setTimeout(updateDataCollectionStatus, 2000);
                })
                .catch(error => {
                    console.error('Error triggering data collection:', error);
                    alert('Error triggering data collection');
                });
        }
    }

    // Initialize data collection status on page load
    document.addEventListener('DOMContentLoaded', function() {
        updateDataCollectionStatus();
        // Refresh status every 5 minutes
        setInterval(updateDataCollectionStatus, 5 * 60 * 1000);
    });
</script>
{% endblock %}
