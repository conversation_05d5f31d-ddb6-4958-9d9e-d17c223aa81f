"""
AI response cache manager for MignalyBot v2

Features:
- In-memory and database caching
- TTL support
- Cache statistics
- Automatic cleanup
- Performance optimization
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union
import pytz

from core.utils.helpers import async_performance_monitor
from database.connection.manager import DatabaseManager
from database.models.content import ContentCache


class CacheManager:
    """AI response cache manager with multiple storage backends"""
    
    def __init__(self, db_manager: DatabaseManager, max_memory_items: int = 1000):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.max_memory_items = max_memory_items
        
        # In-memory cache for frequently accessed items
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        
        # Cache statistics
        self.stats = {
            'memory_hits': 0,
            'database_hits': 0,
            'misses': 0,
            'sets': 0,
            'evictions': 0,
            'cleanup_runs': 0
        }
        
        # Start cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start background cleanup task"""
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(300)  # Run every 5 minutes
                    await self._cleanup_expired()
                except Exception as e:
                    self.logger.error(f"Cache cleanup error: {e}")
        
        self._cleanup_task = asyncio.create_task(cleanup_loop())
    
    async def _cleanup_expired(self):
        """Clean up expired cache entries"""
        try:
            now = datetime.now(pytz.UTC)
            
            # Clean memory cache
            expired_keys = [
                key for key, value in self.memory_cache.items()
                if value['expires_at'] <= now
            ]
            
            for key in expired_keys:
                del self.memory_cache[key]
                self.stats['evictions'] += 1
            
            # Clean database cache
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    # For SQLite, use sync operations
                    from sqlalchemy import delete
                    session.execute(
                        delete(ContentCache).where(ContentCache.expires_at <= now)
                    )
                    session.commit()
                else:
                    # For async databases
                    from sqlalchemy import delete
                    await session.execute(
                        delete(ContentCache).where(ContentCache.expires_at <= now)
                    )
                    await session.commit()
            
            self.stats['cleanup_runs'] += 1
            self.logger.debug(f"Cache cleanup completed. Removed {len(expired_keys)} memory entries")
            
        except Exception as e:
            self.logger.error(f"Error during cache cleanup: {e}")
    
    def _evict_lru_memory(self):
        """Evict least recently used items from memory cache"""
        if len(self.memory_cache) <= self.max_memory_items:
            return
        
        # Sort by last accessed time and remove oldest
        sorted_items = sorted(
            self.memory_cache.items(),
            key=lambda x: x[1]['last_accessed']
        )
        
        items_to_remove = len(self.memory_cache) - self.max_memory_items + 100  # Remove extra for buffer
        
        for i in range(items_to_remove):
            if i < len(sorted_items):
                key = sorted_items[i][0]
                del self.memory_cache[key]
                self.stats['evictions'] += 1
    
    @async_performance_monitor("cache_get")
    async def get(self, key: str) -> Optional[str]:
        """
        Get cached content by key
        
        Args:
            key: Cache key
            
        Returns:
            Cached content or None if not found/expired
        """
        now = datetime.now(pytz.UTC)
        
        # Check memory cache first
        if key in self.memory_cache:
            cache_entry = self.memory_cache[key]
            if cache_entry['expires_at'] > now:
                cache_entry['last_accessed'] = now
                cache_entry['hit_count'] += 1
                self.stats['memory_hits'] += 1
                return cache_entry['content']
            else:
                # Expired, remove from memory
                del self.memory_cache[key]
        
        # Check database cache
        try:
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(
                        select(ContentCache).where(ContentCache.cache_key == key)
                    )
                else:
                    from sqlalchemy import select
                    result = await session.execute(
                        select(ContentCache).where(ContentCache.cache_key == key)
                    )
                
                cache_entry = result.scalars().first()
                
                if cache_entry and cache_entry.expires_at > now:
                    # Update access statistics
                    cache_entry.update_access()
                    
                    if self.db_manager.settings.database.is_sqlite:
                        session.commit()
                    else:
                        await session.commit()
                    
                    # Add to memory cache for faster future access
                    self.memory_cache[key] = {
                        'content': cache_entry.content,
                        'expires_at': cache_entry.expires_at,
                        'last_accessed': now,
                        'hit_count': 1
                    }
                    
                    self._evict_lru_memory()
                    
                    self.stats['database_hits'] += 1
                    return cache_entry.content
                
                elif cache_entry:
                    # Expired, remove from database
                    if self.db_manager.settings.database.is_sqlite:
                        session.delete(cache_entry)
                        session.commit()
                    else:
                        await session.delete(cache_entry)
                        await session.commit()
        
        except Exception as e:
            self.logger.error(f"Error retrieving from cache: {e}")
        
        self.stats['misses'] += 1
        return None
    
    @async_performance_monitor("cache_set")
    async def set(
        self,
        key: str,
        content: str,
        ttl: int = 3600,
        content_type: str = "ai_response",
        language: Optional[str] = None,
        symbols: Optional[str] = None
    ):
        """
        Set cached content
        
        Args:
            key: Cache key
            content: Content to cache
            ttl: Time to live in seconds
            content_type: Type of content
            language: Content language
            symbols: Related symbols
        """
        now = datetime.now(pytz.UTC)
        expires_at = now + timedelta(seconds=ttl)
        
        try:
            # Add to memory cache
            self.memory_cache[key] = {
                'content': content,
                'expires_at': expires_at,
                'last_accessed': now,
                'hit_count': 0
            }
            
            self._evict_lru_memory()
            
            # Add to database cache
            async with self.db_manager.get_async_session() as session:
                # Check if entry already exists
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select
                    result = session.execute(
                        select(ContentCache).where(ContentCache.cache_key == key)
                    )
                else:
                    from sqlalchemy import select
                    result = await session.execute(
                        select(ContentCache).where(ContentCache.cache_key == key)
                    )
                
                existing_entry = result.scalars().first()
                
                if existing_entry:
                    # Update existing entry
                    existing_entry.content = content
                    existing_entry.expires_at = expires_at
                    existing_entry.last_accessed = now
                    existing_entry.hit_count = 0
                else:
                    # Create new entry
                    cache_entry = ContentCache(
                        cache_key=key,
                        content=content,
                        content_type=content_type,
                        expires_at=expires_at,
                        language=language,
                        symbols=symbols
                    )
                    session.add(cache_entry)
                
                if self.db_manager.settings.database.is_sqlite:
                    session.commit()
                else:
                    await session.commit()
            
            self.stats['sets'] += 1
            
        except Exception as e:
            self.logger.error(f"Error setting cache: {e}")
    
    async def delete(self, key: str) -> bool:
        """
        Delete cached content by key
        
        Args:
            key: Cache key
            
        Returns:
            True if deleted, False if not found
        """
        deleted = False
        
        # Remove from memory cache
        if key in self.memory_cache:
            del self.memory_cache[key]
            deleted = True
        
        # Remove from database cache
        try:
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import delete
                    result = session.execute(
                        delete(ContentCache).where(ContentCache.cache_key == key)
                    )
                    session.commit()
                else:
                    from sqlalchemy import delete
                    result = await session.execute(
                        delete(ContentCache).where(ContentCache.cache_key == key)
                    )
                    await session.commit()
                
                if result.rowcount > 0:
                    deleted = True
        
        except Exception as e:
            self.logger.error(f"Error deleting from cache: {e}")
        
        return deleted
    
    async def clear(self, content_type: Optional[str] = None):
        """
        Clear cache entries
        
        Args:
            content_type: Optional content type filter
        """
        try:
            # Clear memory cache
            if content_type:
                # For memory cache, we don't store content_type, so clear all
                self.memory_cache.clear()
            else:
                self.memory_cache.clear()
            
            # Clear database cache
            async with self.db_manager.get_async_session() as session:
                if content_type:
                    if self.db_manager.settings.database.is_sqlite:
                        from sqlalchemy import delete
                        session.execute(
                            delete(ContentCache).where(ContentCache.content_type == content_type)
                        )
                        session.commit()
                    else:
                        from sqlalchemy import delete
                        await session.execute(
                            delete(ContentCache).where(ContentCache.content_type == content_type)
                        )
                        await session.commit()
                else:
                    if self.db_manager.settings.database.is_sqlite:
                        from sqlalchemy import delete
                        session.execute(delete(ContentCache))
                        session.commit()
                    else:
                        from sqlalchemy import delete
                        await session.execute(delete(ContentCache))
                        await session.commit()
        
        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        stats = self.stats.copy()
        stats.update({
            'memory_cache_size': len(self.memory_cache),
            'memory_cache_max_size': self.max_memory_items
        })
        
        # Get database cache statistics
        try:
            async with self.db_manager.get_async_session() as session:
                if self.db_manager.settings.database.is_sqlite:
                    from sqlalchemy import select, func
                    result = session.execute(
                        select(func.count(ContentCache.id))
                    )
                else:
                    from sqlalchemy import select, func
                    result = await session.execute(
                        select(func.count(ContentCache.id))
                    )
                
                db_cache_size = result.scalar()
                stats['database_cache_size'] = db_cache_size
        
        except Exception as e:
            self.logger.error(f"Error getting cache stats: {e}")
            stats['database_cache_size'] = 0
        
        return stats
    
    async def close(self):
        """Close cache manager and cleanup"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        await self._cleanup_expired()
