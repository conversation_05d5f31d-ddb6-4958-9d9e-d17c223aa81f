# MignalyBot Database Management Scripts

This directory contains utility scripts for managing the MignalyBot database.

## Scripts Overview

### 1. `remove_all_posts.py`
Removes all posts from the database.

**Usage:**
```bash
# Interactive mode (asks for confirmation)
python scripts/remove_all_posts.py

# Force mode (no confirmation)
python scripts/remove_all_posts.py --force
```

**What it does:**
- Connects to the MignalyBot database
- Counts and displays total posts
- Asks for confirmation (unless --force is used)
- Deletes all posts from the `posts` table
- Provides feedback on the operation

### 2. `remove_all_signals.py`
Removes all trading signals from the database.

**Usage:**
```bash
# Interactive mode (asks for confirmation)
python scripts/remove_all_signals.py

# Force mode (no confirmation)
python scripts/remove_all_signals.py --force
```

**What it does:**
- Connects to the MignalyBot database
- Counts and displays signal statistics by status
- Asks for confirmation (unless --force is used)
- Deletes all signals from the `trading_signals` table
- Provides feedback on the operation

### 3. `backup_configurations.py`
Creates backups of strategies, prompt templates, and channel configurations.

**Usage:**
```bash
# Basic backup (separate files)
python scripts/backup_configurations.py

# Backup to specific directory
python scripts/backup_configurations.py --output-dir /path/to/backup

# Create single consolidated file
python scripts/backup_configurations.py --single-file

# Specify output directory and single file
python scripts/backup_configurations.py --output-dir ./my_backup --single-file
```

**What it does:**
- Exports all strategies from the `strategies` table
- Exports all prompt templates from the `prompt_templates` table
- Exports all channel configurations from the `channels` table
- Creates timestamped backup directory
- Saves data as JSON files
- Creates a backup summary

**Output structure:**
```
backups/
└── config_backup_YYYYMMDD_HHMMSS/
    ├── strategies.json
    ├── prompt_templates.json
    ├── channels.json
    ├── backup_summary.json
    └── consolidated_backup.json (if --single-file used)
```

## Prerequisites

1. **Environment Setup**: Make sure you have a `.env` file in the project root with the correct `DATABASE_URL`.

2. **Dependencies**: Install required Python packages:
   ```bash
   pip install -r requirements.txt
   ```

3. **Database Access**: Ensure the database is accessible and not locked by other processes.

## Safety Features

- **Confirmation Prompts**: Deletion scripts ask for confirmation before proceeding
- **Force Mode**: Use `--force` flag to skip confirmations (useful for automation)
- **Error Handling**: Scripts handle database errors gracefully
- **Logging**: Detailed logging for all operations
- **Rollback**: Database transactions are rolled back on errors

## Examples

### Clean Database for Testing
```bash
# Remove all posts and signals for a fresh start
python scripts/remove_all_posts.py --force
python scripts/remove_all_signals.py --force
```

### Backup Before Major Changes
```bash
# Create a backup before making changes
python scripts/backup_configurations.py --single-file
```

### Automated Cleanup
```bash
# Use in scripts or cron jobs
python scripts/remove_all_posts.py --force
python scripts/remove_all_signals.py --force
```

## Important Notes

⚠️ **WARNING**: The deletion scripts permanently remove data from the database. Always create backups before running deletion scripts in production.

- These scripts work with both SQLite and PostgreSQL databases
- The scripts automatically detect the database type from the `DATABASE_URL`
- All scripts include proper error handling and logging
- Backup files are saved in JSON format for easy inspection and restoration

## Troubleshooting

1. **Import Errors**: Make sure you're running the scripts from the project root directory
2. **Database Connection**: Check your `.env` file and `DATABASE_URL` setting
3. **Permission Issues**: Ensure the script has write permissions for backup directories
4. **Database Locked**: Close other database connections before running scripts
