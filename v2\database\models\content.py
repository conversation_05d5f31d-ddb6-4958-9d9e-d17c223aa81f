"""
Content models for MignalyBot v2

Optimized models for:
- News items
- Economic events
- AI-generated content
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import (
    Column, Integer, String, Float, Boolean, DateTime, Text,
    Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
import pytz

from .base import Base


class NewsItem(Base):
    """News item model"""
    __tablename__ = "news_items"
    
    # News content
    title = Column(String(500), nullable=False, index=True)
    content = Column(Text, nullable=True)
    summary = Column(Text, nullable=True)  # AI-generated summary
    
    # Source information
    source = Column(String(100), nullable=False, index=True)
    url = Column(String(1000), nullable=True, unique=True, index=True)
    author = Column(String(255), nullable=True)
    
    # Media
    image_url = Column(String(1000), nullable=True)
    video_url = Column(String(1000), nullable=True)
    
    # Timing
    published_at = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Content analysis
    sentiment_score = Column(Float, nullable=True)  # -1 to 1
    importance_score = Column(Float, default=0.5)  # 0 to 1
    symbols = Column(String(255), nullable=True, index=True)  # Related symbols
    categories = Column(String(255), nullable=True, index=True)  # News categories
    
    # AI analysis
    ai_analysis = Column(Text, nullable=True)
    ai_tags = Column(String(500), nullable=True)  # AI-generated tags
    market_impact = Column(String(50), nullable=True)  # bullish/bearish/neutral
    
    # Processing status
    processed = Column(Boolean, default=False, index=True)
    content_hash = Column(String(32), nullable=True, index=True)  # For duplicate detection
    
    # Relationships
    posts = relationship("Post", back_populates="news_item")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_news_published_processed', published_at.desc(), processed),
        Index('idx_news_source_published', source, published_at.desc()),
        Index('idx_news_symbols', symbols),
        Index('idx_news_importance', importance_score.desc()),
        Index('idx_news_sentiment', sentiment_score),
        Index('idx_news_content_hash', content_hash),
    )
    
    @property
    def is_recent(self) -> bool:
        """Check if news is recent (within 24 hours)"""
        if not self.published_at:
            return False
        now = datetime.now(pytz.UTC)
        return (now - self.published_at).total_seconds() < 86400  # 24 hours
    
    @property
    def related_symbols_list(self) -> List[str]:
        """Get list of related symbols"""
        if not self.symbols:
            return []
        return [s.strip() for s in self.symbols.split(',')]


class EconomicEvent(Base):
    """Economic calendar event model"""
    __tablename__ = "economic_events"
    
    # Event details
    title = Column(String(500), nullable=False, index=True)
    description = Column(Text, nullable=True)
    country = Column(String(50), nullable=True, index=True)
    currency = Column(String(10), nullable=True, index=True)
    
    # Event timing
    event_time = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Impact and importance
    impact = Column(Integer, nullable=True, index=True)  # 1-3 scale (low-high)
    importance_score = Column(Float, default=0.5)  # 0-1 calculated importance
    
    # Economic data
    previous = Column(String(50), nullable=True)
    forecast = Column(String(50), nullable=True)
    actual = Column(String(50), nullable=True)
    
    # Market analysis
    market_expectation = Column(String(100), nullable=True)
    potential_impact = Column(Text, nullable=True)
    ai_analysis = Column(Text, nullable=True)
    
    # Processing status
    processed = Column(Boolean, default=False, index=True)
    result_processed = Column(Boolean, default=False, index=True)
    content_hash = Column(String(32), nullable=True, index=True)
    
    # Relationships
    posts = relationship("Post", back_populates="economic_event")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_event_time_impact', event_time, impact),
        Index('idx_event_country_time', country, event_time),
        Index('idx_event_processed', processed, event_time),
        Index('idx_event_importance', importance_score.desc()),
        Index('idx_event_content_hash', content_hash),
    )
    
    @property
    def is_upcoming(self) -> bool:
        """Check if event is upcoming"""
        if not self.event_time:
            return False
        now = datetime.now(pytz.UTC)
        return self.event_time > now
    
    @property
    def is_high_impact(self) -> bool:
        """Check if event is high impact"""
        return self.impact == 3 or (self.importance_score and self.importance_score > 0.7)
    
    @property
    def time_until_event(self) -> Optional[float]:
        """Get hours until event"""
        if not self.event_time:
            return None
        now = datetime.now(pytz.UTC)
        if self.event_time <= now:
            return 0
        return (self.event_time - now).total_seconds() / 3600


class AIContent(Base):
    """AI-generated content model"""
    __tablename__ = "ai_content"
    
    # Content details
    content_type = Column(String(50), nullable=False, index=True)  # analysis/strategy/summary
    title = Column(String(500), nullable=True)
    content = Column(Text, nullable=False)
    
    # AI metadata
    ai_model = Column(String(100), nullable=False)
    prompt = Column(Text, nullable=True)
    temperature = Column(Float, nullable=True)
    max_tokens = Column(Integer, nullable=True)
    
    # Content context
    language = Column(String(10), nullable=False, index=True)
    symbols = Column(String(255), nullable=True, index=True)
    timeframe = Column(String(10), nullable=True, index=True)
    market_conditions = Column(String(100), nullable=True)
    
    # Quality metrics
    quality_score = Column(Float, nullable=True)  # 0-1 quality assessment
    relevance_score = Column(Float, nullable=True)  # 0-1 relevance score
    uniqueness_score = Column(Float, nullable=True)  # 0-1 uniqueness score
    
    # Usage tracking
    used_count = Column(Integer, default=0)
    last_used = Column(DateTime(timezone=True), nullable=True)
    
    # Content hash for caching and duplicate detection
    content_hash = Column(String(32), nullable=True, index=True)
    prompt_hash = Column(String(32), nullable=True, index=True)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_ai_content_type_language', content_type, language),
        Index('idx_ai_content_hash', content_hash),
        Index('idx_ai_prompt_hash', prompt_hash),
        Index('idx_ai_quality', quality_score.desc()),
        Index('idx_ai_symbols_timeframe', symbols, timeframe),
    )
    
    @property
    def is_high_quality(self) -> bool:
        """Check if content is high quality"""
        return self.quality_score is not None and self.quality_score > 0.7
    
    @property
    def is_fresh(self) -> bool:
        """Check if content is fresh (created within last hour)"""
        now = datetime.now(pytz.UTC)
        return (now - self.created_at).total_seconds() < 3600  # 1 hour


class ContentCache(Base):
    """Content caching model for performance optimization"""
    __tablename__ = "content_cache"
    
    # Cache key and content
    cache_key = Column(String(255), nullable=False, unique=True, index=True)
    content = Column(Text, nullable=False)
    content_type = Column(String(50), nullable=False, index=True)
    
    # Cache metadata
    expires_at = Column(DateTime(timezone=True), nullable=False, index=True)
    hit_count = Column(Integer, default=0)
    last_accessed = Column(DateTime(timezone=True), nullable=True)
    
    # Content metadata
    language = Column(String(10), nullable=True, index=True)
    symbols = Column(String(255), nullable=True)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_cache_expires', expires_at),
        Index('idx_cache_type_language', content_type, language),
        Index('idx_cache_hit_count', hit_count.desc()),
    )
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        now = datetime.now(pytz.UTC)
        return now > self.expires_at
    
    def update_access(self):
        """Update access statistics"""
        self.hit_count += 1
        self.last_accessed = datetime.now(pytz.UTC)
