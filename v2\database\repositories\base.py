"""
Base repository class for MignalyBot v2

Provides optimized database operations with:
- Batch operations
- Query optimization
- Caching
- Performance monitoring
"""

import asyncio
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic, Union
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from core.utils.helpers import async_performance_monitor, BatchProcessor
from core.exceptions.base import DatabaseException
from database.models.base import Base

T = TypeVar('T', bound=Base)


class BaseRepository(Generic[T]):
    """Base repository with optimized database operations"""
    
    def __init__(self, model: Type[T], db_manager):
        self.model = model
        self.db_manager = db_manager
        self.batch_processor = BatchProcessor()
    
    @async_performance_monitor("repository_get_by_id")
    async def get_by_id(self, id: int) -> Optional[T]:
        """Get entity by ID"""
        async with self.db_manager.get_async_session() as session:
            if self.db_manager.settings.database.is_sqlite:
                result = session.execute(select(self.model).where(self.model.id == id))
            else:
                result = await session.execute(select(self.model).where(self.model.id == id))
            return result.scalars().first()
    
    @async_performance_monitor("repository_get_all")
    async def get_all(
        self,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[str] = None
    ) -> List[T]:
        """Get all entities with optional pagination and ordering"""
        async with self.db_manager.get_async_session() as session:
            query = select(self.model)
            
            # Add ordering
            if order_by:
                if hasattr(self.model, order_by):
                    attr = getattr(self.model, order_by)
                    query = query.order_by(attr.desc() if order_by.endswith('_desc') else attr)
            
            # Add pagination
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            
            if self.db_manager.settings.database.is_sqlite:
                result = session.execute(query)
            else:
                result = await session.execute(query)
            
            return result.scalars().all()
    
    @async_performance_monitor("repository_get_by_filter")
    async def get_by_filter(self, **filters) -> List[T]:
        """Get entities by filter criteria"""
        async with self.db_manager.get_async_session() as session:
            query = select(self.model)
            
            # Build filter conditions
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    attr = getattr(self.model, key)
                    if isinstance(value, list):
                        conditions.append(attr.in_(value))
                    elif isinstance(value, dict):
                        # Handle range queries
                        if 'gte' in value:
                            conditions.append(attr >= value['gte'])
                        if 'lte' in value:
                            conditions.append(attr <= value['lte'])
                        if 'gt' in value:
                            conditions.append(attr > value['gt'])
                        if 'lt' in value:
                            conditions.append(attr < value['lt'])
                    else:
                        conditions.append(attr == value)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            if self.db_manager.settings.database.is_sqlite:
                result = session.execute(query)
            else:
                result = await session.execute(query)
            
            return result.scalars().all()
    
    @async_performance_monitor("repository_create")
    async def create(self, **kwargs) -> T:
        """Create new entity"""
        async with self.db_manager.get_async_session() as session:
            entity = self.model(**kwargs)
            session.add(entity)
            
            if self.db_manager.settings.database.is_sqlite:
                session.commit()
                session.refresh(entity)
            else:
                await session.commit()
                await session.refresh(entity)
            
            return entity
    
    @async_performance_monitor("repository_create_batch")
    async def create_batch(self, entities_data: List[Dict[str, Any]]) -> List[T]:
        """Create multiple entities in batch"""
        if not entities_data:
            return []
        
        async with self.db_manager.get_async_session() as session:
            entities = [self.model(**data) for data in entities_data]
            session.add_all(entities)
            
            if self.db_manager.settings.database.is_sqlite:
                session.commit()
                for entity in entities:
                    session.refresh(entity)
            else:
                await session.commit()
                for entity in entities:
                    await session.refresh(entity)
            
            return entities
    
    @async_performance_monitor("repository_update")
    async def update(self, id: int, **kwargs) -> Optional[T]:
        """Update entity by ID"""
        async with self.db_manager.get_async_session() as session:
            if self.db_manager.settings.database.is_sqlite:
                entity = session.execute(
                    select(self.model).where(self.model.id == id)
                ).scalars().first()
                
                if entity:
                    for key, value in kwargs.items():
                        if hasattr(entity, key):
                            setattr(entity, key, value)
                    session.commit()
                    session.refresh(entity)
            else:
                result = await session.execute(
                    select(self.model).where(self.model.id == id)
                )
                entity = result.scalars().first()
                
                if entity:
                    for key, value in kwargs.items():
                        if hasattr(entity, key):
                            setattr(entity, key, value)
                    await session.commit()
                    await session.refresh(entity)
            
            return entity
    
    @async_performance_monitor("repository_update_batch")
    async def update_batch(self, updates: List[Dict[str, Any]]) -> int:
        """Update multiple entities in batch"""
        if not updates:
            return 0
        
        updated_count = 0
        async with self.db_manager.get_async_session() as session:
            for update_data in updates:
                entity_id = update_data.pop('id', None)
                if entity_id:
                    if self.db_manager.settings.database.is_sqlite:
                        result = session.execute(
                            update(self.model)
                            .where(self.model.id == entity_id)
                            .values(**update_data)
                        )
                    else:
                        result = await session.execute(
                            update(self.model)
                            .where(self.model.id == entity_id)
                            .values(**update_data)
                        )
                    updated_count += result.rowcount
            
            if self.db_manager.settings.database.is_sqlite:
                session.commit()
            else:
                await session.commit()
        
        return updated_count
    
    @async_performance_monitor("repository_delete")
    async def delete(self, id: int) -> bool:
        """Delete entity by ID"""
        async with self.db_manager.get_async_session() as session:
            if self.db_manager.settings.database.is_sqlite:
                result = session.execute(
                    delete(self.model).where(self.model.id == id)
                )
                session.commit()
            else:
                result = await session.execute(
                    delete(self.model).where(self.model.id == id)
                )
                await session.commit()
            
            return result.rowcount > 0
    
    @async_performance_monitor("repository_delete_batch")
    async def delete_batch(self, ids: List[int]) -> int:
        """Delete multiple entities by IDs"""
        if not ids:
            return 0
        
        async with self.db_manager.get_async_session() as session:
            if self.db_manager.settings.database.is_sqlite:
                result = session.execute(
                    delete(self.model).where(self.model.id.in_(ids))
                )
                session.commit()
            else:
                result = await session.execute(
                    delete(self.model).where(self.model.id.in_(ids))
                )
                await session.commit()
            
            return result.rowcount
    
    @async_performance_monitor("repository_count")
    async def count(self, **filters) -> int:
        """Count entities with optional filters"""
        async with self.db_manager.get_async_session() as session:
            query = select(func.count(self.model.id))
            
            # Build filter conditions
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    attr = getattr(self.model, key)
                    conditions.append(attr == value)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            if self.db_manager.settings.database.is_sqlite:
                result = session.execute(query)
            else:
                result = await session.execute(query)
            
            return result.scalar()
    
    @async_performance_monitor("repository_exists")
    async def exists(self, **filters) -> bool:
        """Check if entity exists with given filters"""
        async with self.db_manager.get_async_session() as session:
            query = select(self.model.id)
            
            # Build filter conditions
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model, key):
                    attr = getattr(self.model, key)
                    conditions.append(attr == value)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            query = query.limit(1)
            
            if self.db_manager.settings.database.is_sqlite:
                result = session.execute(query)
            else:
                result = await session.execute(query)
            
            return result.first() is not None
    
    async def get_or_create(self, defaults: Optional[Dict[str, Any]] = None, **kwargs) -> tuple[T, bool]:
        """Get existing entity or create new one"""
        entity = await self.get_by_filter(**kwargs)
        
        if entity:
            return entity[0], False
        
        # Create new entity
        create_data = kwargs.copy()
        if defaults:
            create_data.update(defaults)
        
        new_entity = await self.create(**create_data)
        return new_entity, True
