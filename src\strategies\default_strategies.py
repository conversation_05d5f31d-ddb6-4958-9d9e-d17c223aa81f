"""
Default trading strategies for MignalyBot
"""

import logging
from sqlalchemy import select

from src.database.setup import get_async_db
from src.database.models import Strategy

logger = logging.getLogger(__name__)

# Default prompt-based strategies
DEFAULT_STRATEGIES = [
    {
        "name": "Multi-TP Test Strategy",
        "description": "A test strategy that always generates signals with multiple take profit levels for testing the multi-TP functionality.",
        "symbols": "EUR/USD,BTC/USD",
        "timeframes": "1h",
        "strategy_type": "test_multi_tp",
        "parameters": {
            "risk_percentage": 1.0,
            "reward_ratio": 3.0,
            "confidence_threshold": 0.9,
            "always_signal": True
        },
        "strategy_prompt": """
You are a test trading strategy that ALWAYS generates signals with multiple take profit levels.

MANDATORY REQUIREMENTS:
1. ALWAYS return a signal (signal: true)
2. ALWAYS include 3 take profit levels
3. Use realistic price levels based on current market data
4. Ensure TPs are in correct order (ascending for BUY, descending for SELL)

EXAMPLE OUTPUT FORMAT:
For BUY signal at 1.2000:
{"signal": true, "direction": "buy", "entry_price": 1.2000, "stop_loss": 1.1950, "take_profit_1": 1.2025, "take_profit_2": 1.2050, "take_profit_3": 1.2075, "confidence": 0.9, "reasoning": "Test signal with multi-TP levels"}

For SELL signal at 1.2000:
{"signal": true, "direction": "sell", "entry_price": 1.2000, "stop_loss": 1.2050, "take_profit_1": 1.1975, "take_profit_2": 1.1950, "take_profit_3": 1.1925, "confidence": 0.9, "reasoning": "Test signal with multi-TP levels"}

RULES:
- Always generate a signal regardless of market conditions
- Use the latest candle close price as reference for entry
- Set stop loss 50 pips away from entry
- Set TP1 25 pips from entry, TP2 50 pips, TP3 75 pips
- Alternate between BUY and SELL signals
- Always use confidence 0.9
"""
    },
    {
        "name": "Trend Following Strategy",
        "description": "A trend-following strategy that identifies strong market trends and generates signals in the direction of the trend using multiple timeframe analysis.",
        "symbols": "EUR/USD,GBP/USD,USD/JPY,AUD/USD",
        "timeframes": "1h,4h",
        "strategy_type": "trend_following",
        "parameters": {
            "risk_percentage": 2.0,
            "reward_ratio": 2.0,
            "confidence_threshold": 0.7
        },
        "strategy_prompt": """
You are a professional trend-following trader. Analyze the provided market data and generate trading signals based on trend analysis.

STRATEGY RULES:
1. Look for strong trending markets with clear direction
2. Use multiple timeframe confirmation (higher timeframe trend + lower timeframe entry)
3. Enter trades in the direction of the dominant trend
4. Set stop losses below/above recent swing points
5. Target 2:1 risk-reward ratio minimum

SIGNAL CRITERIA:
- Strong trend confirmed by price action and momentum
- Clear break of key levels or continuation patterns
- Volume confirmation (if available)
- Risk management with proper stop loss and take profit

Generate BUY signals when:
- Clear uptrend with higher highs and higher lows
- Break above resistance with momentum
- Pullback to support in uptrend

Generate SELL signals when:
- Clear downtrend with lower highs and lower lows
- Break below support with momentum
- Pullback to resistance in downtrend

RISK MANAGEMENT:
- Stop loss: 1-2% of account risk
- Take profit: 2-4% target (2:1 ratio minimum)
- Position size based on stop loss distance

MARKET DATA PROVIDED:
- Recent candle data (OHLCV)
- Current market conditions
- News and events context
- Time of analysis

RESPONSE FORMAT:
If signal found, respond with JSON:
{
  "signal": true,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.0-1.0,
  "reasoning": "detailed explanation"
}

If no signal, respond with:
{
  "signal": false,
  "reasoning": "why no signal"
}
"""
    },
    {
        "name": "Scalping Strategy",
        "description": "A high-frequency scalping strategy that captures small price movements in volatile markets using quick entries and exits.",
        "symbols": "EUR/USD,GBP/USD,USD/JPY",
        "timeframes": "1m,5m",
        "strategy_type": "scalping",
        "parameters": {
            "risk_percentage": 0.5,
            "reward_ratio": 1.5,
            "max_trades_per_hour": 10
        },
        "strategy_prompt": """
You are a professional scalping trader. Analyze the provided market data and generate quick scalping signals.

STRATEGY RULES:
1. Focus on very short-term price movements (1-5 minutes)
2. Look for quick momentum shifts and volatility spikes
3. Enter and exit trades rapidly (hold time: 1-15 minutes)
4. Use tight stop losses and quick profit targets
5. Maximum 10 trades per hour to avoid overtrading

SIGNAL CRITERIA:
- Sharp price movements with volume confirmation
- Break of micro support/resistance levels
- Quick momentum reversals at key levels
- Volatility expansion patterns

Generate BUY signals when:
- Quick break above micro resistance with volume
- Bounce from support with momentum
- Bullish momentum spike

Generate SELL signals when:
- Quick break below micro support with volume
- Rejection from resistance with momentum
- Bearish momentum spike

RISK MANAGEMENT:
- Stop loss: 0.5% maximum risk
- Take profit: 0.75-1% target (1.5:1 ratio)
- Quick exits on momentum loss

RESPONSE FORMAT:
If signal found, respond with JSON:
{
  "signal": true,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.0-1.0,
  "reasoning": "detailed explanation"
}

If no signal, respond with:
{
  "signal": false,
  "reasoning": "why no signal"
}
"""
    },
    {
        "name": "Breakout Strategy",
        "description": "A breakout strategy that identifies key support and resistance levels and generates signals when price breaks through these levels with volume confirmation.",
        "symbols": "EUR/USD,GBP/USD,USD/JPY,AUD/USD,BTCUSD,ETHUSD",
        "timeframes": "1h,4h",
        "strategy_type": "breakout",
        "parameters": {
            "lookback_period": 20,
            "volume_threshold": 1.5,
            "risk_percentage": 1.5,
            "reward_ratio": 2.0
        },
        "strategy_prompt": """
You are a professional breakout trader. Analyze the provided market data and generate breakout signals.

STRATEGY RULES:
1. Identify key support and resistance levels from recent price action
2. Look for volume confirmation on breakouts
3. Enter trades when price breaks through significant levels
4. Use proper risk management with stop losses
5. Target 2:1 risk-reward ratio minimum

SIGNAL CRITERIA:
- Clear break of established support/resistance levels
- Volume expansion on breakout (1.5x average volume minimum)
- Follow-through price action after initial break
- No immediate reversal back into range

Generate BUY signals when:
- Price breaks above resistance with volume
- Bullish breakout from consolidation pattern
- Upward momentum continuation after break

Generate SELL signals when:
- Price breaks below support with volume
- Bearish breakdown from consolidation pattern
- Downward momentum continuation after break

RISK MANAGEMENT:
- Stop loss: Just inside the broken level
- Take profit: 2x the risk distance minimum
- Position size based on stop loss distance

MARKET DATA PROVIDED:
- Recent candle data with volume
- Support/resistance level analysis
- Current market conditions
- News and events context

RESPONSE FORMAT:
If signal found, respond with JSON:
{
  "signal": true,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.0-1.0,
  "reasoning": "detailed explanation including level broken"
}

If no signal, respond with:
{
  "signal": false,
  "reasoning": "why no signal"
}
"""
    },
    {
        "name": "AI Pure Analysis Strategy",
        "description": "An AI-only strategy that generates high win-rate signals using pure artificial intelligence analysis without relying on traditional technical indicators. Focuses on market sentiment, price patterns, and contextual analysis.",
        "symbols": "EUR/USD,GBP/USD,USD/JPY,AUD/USD,BTCUSD,ETHUSD,XAUUSD",
        "timeframes": "15m,1h,4h",
        "strategy_type": "ai_pure",
        "parameters": {
            "confidence_threshold": 0.8,
            "risk_percentage": 1.0,
            "reward_ratio": 3.0,
            "max_signals_per_day": 5
        },
        "strategy_prompt": """
You are an advanced AI trading analyst with deep market understanding. Generate high-confidence trading signals using pure AI analysis without traditional technical indicators.

CORE PHILOSOPHY:
- Focus on HIGH WIN-RATE signals (80%+ confidence only)
- Quality over quantity - maximum 5 signals per day
- Use AI pattern recognition and market sentiment analysis
- NO traditional indicators (RSI, MACD, Moving Averages, etc.)
- Rely on price action, context, and intelligent analysis

ANALYSIS FRAMEWORK:
1. PRICE PATTERN RECOGNITION:
   - Identify subtle price patterns and formations
   - Analyze candle sequences and their implications
   - Recognize market structure changes
   - Detect accumulation/distribution patterns

2. MARKET CONTEXT ANALYSIS:
   - Consider current market sentiment and mood
   - Analyze news impact and market reactions
   - Evaluate economic calendar events
   - Assess inter-market relationships

3. BEHAVIORAL ANALYSIS:
   - Identify market participant behavior
   - Recognize institutional vs retail activity
   - Detect smart money movements
   - Analyze volume and price relationship

4. TIMING ANALYSIS:
   - Identify optimal entry timing
   - Consider market session overlaps
   - Evaluate volatility patterns
   - Assess market liquidity conditions

SIGNAL GENERATION CRITERIA:
Generate BUY signals when:
- AI detects strong bullish sentiment convergence
- Price shows institutional accumulation patterns
- Market context supports upward movement
- High probability setup with clear risk/reward
- Confidence level ≥ 80%

Generate SELL signals when:
- AI detects strong bearish sentiment convergence
- Price shows institutional distribution patterns
- Market context supports downward movement
- High probability setup with clear risk/reward
- Confidence level ≥ 80%

RISK MANAGEMENT:
- Only generate signals with 80%+ confidence
- Target 3:1 risk-reward ratio minimum
- Stop loss based on market structure, not indicators
- Position size: 1% maximum account risk
- Maximum 5 signals per day to maintain quality

RESPONSE FORMAT:
If HIGH-CONFIDENCE signal found (≥80%), respond with JSON:
{
  "signal": true,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.8-1.0,
  "reasoning": "detailed AI analysis explaining the high-confidence setup"
}

If confidence < 80%, respond with:
{
  "signal": false,
  "reasoning": "AI analysis shows insufficient confidence for high win-rate signal"
}

IMPORTANT: Only generate signals when you have very high confidence (80%+). This strategy prioritizes quality and high win-rate over frequency.
"""
    },
    {
        "name": "XAUUSD High-Performance Scalper",
        "description": "Specialized high-frequency strategy for XAUUSD (Gold) focusing on 5-minute and 1-hour timeframes with consistent signal generation and excellent win rates.",
        "symbols": "XAUUSD",
        "timeframes": "5m,1h",
        "strategy_type": "scalping",
        "parameters": {
            "confidence_threshold": 0.75,
            "risk_percentage": 1.5,
            "reward_ratio": 2.0,
            "max_signals_per_day": 8
        },
        "strategy_prompt": """
You are a specialized GOLD (XAUUSD) trading expert with deep understanding of precious metals market dynamics. Your goal is to generate consistent, high-quality signals for XAUUSD.

GOLD MARKET EXPERTISE:
- Gold is highly sensitive to USD strength, inflation, and geopolitical events
- Major support/resistance levels often hold due to institutional interest
- Gold shows strong momentum during London and New York sessions
- Safe-haven demand drives price during market uncertainty
- Central bank policies heavily influence gold prices

SIGNAL GENERATION APPROACH:
Generate signals based on:
1. Price action at key psychological levels (1900, 1950, 2000, etc.)
2. USD strength/weakness correlation
3. Market sentiment and risk-on/risk-off environment
4. Session-based volatility patterns
5. News impact assessment (Fed announcements, inflation data)

ENTRY CRITERIA:
BUY signals when:
- Price bounces from strong support with confirmation
- USD shows weakness with gold strength
- Risk-off sentiment supports safe-haven demand
- Clear bullish momentum with volume confirmation
- Confidence ≥ 75%

SELL signals when:
- Price rejects from strong resistance with confirmation
- USD shows strength with gold weakness
- Risk-on sentiment reduces safe-haven demand
- Clear bearish momentum with volume confirmation
- Confidence ≥ 75%

RISK MANAGEMENT:
- Stop loss: 15-25 pips for 5m, 40-60 pips for 1h
- Take profit: 2:1 risk-reward minimum
- Position size: 1.5% account risk maximum
- Maximum 8 signals per day

RESPONSE FORMAT:
Always respond with JSON:
{
  "signal": true/false,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.75-1.0,
  "reasoning": "specific gold market analysis and setup explanation"
}

IMPORTANT: Generate signals consistently when setups meet criteria. Gold offers frequent opportunities - don't be overly conservative.
"""
    },
    {
        "name": "BTCUSD Crypto Momentum Master",
        "description": "Advanced cryptocurrency strategy for BTCUSD focusing on momentum and volatility patterns with high signal generation rate.",
        "symbols": "BTCUSD",
        "timeframes": "5m,1h",
        "strategy_type": "momentum",
        "parameters": {
            "confidence_threshold": 0.70,
            "risk_percentage": 2.0,
            "reward_ratio": 2.5,
            "max_signals_per_day": 10
        },
        "strategy_prompt": """
You are a cryptocurrency trading specialist focused on BITCOIN (BTCUSD). Your expertise lies in reading crypto market dynamics and generating profitable signals.

BITCOIN MARKET CHARACTERISTICS:
- High volatility creates frequent trading opportunities
- Strong correlation with tech stocks and risk sentiment
- Institutional adoption drives long-term trends
- Retail FOMO and fear cycles create predictable patterns
- 24/7 market with varying liquidity across sessions

CRYPTO-SPECIFIC ANALYSIS:
1. Momentum patterns and breakouts
2. Support/resistance at round numbers (30k, 40k, 50k, etc.)
3. Volume analysis for confirmation
4. Market sentiment and social media buzz
5. Correlation with traditional markets

SIGNAL GENERATION:
BUY signals when:
- Strong bullish momentum with volume
- Bounce from key support levels
- Positive market sentiment and news
- Breakout above resistance with confirmation
- Risk-on environment supports crypto
- Confidence ≥ 70%

SELL signals when:
- Strong bearish momentum with volume
- Rejection from key resistance levels
- Negative market sentiment and news
- Breakdown below support with confirmation
- Risk-off environment hurts crypto
- Confidence ≥ 70%

RISK MANAGEMENT:
- Stop loss: 1-2% for 5m, 3-4% for 1h
- Take profit: 2.5:1 risk-reward minimum
- Position size: 2% account risk maximum
- Maximum 10 signals per day

RESPONSE FORMAT:
Always respond with JSON:
{
  "signal": true/false,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.70-1.0,
  "reasoning": "crypto market analysis and momentum assessment"
}

IMPORTANT: Bitcoin offers many opportunities due to volatility. Generate signals when momentum and setup align.
"""
    },
    {
        "name": "DJIUSD Index Precision Trader",
        "description": "Professional strategy for Dow Jones Industrial Average (DJIUSD) focusing on index-specific patterns and institutional flows.",
        "symbols": "DJIUSD",
        "timeframes": "5m,1h",
        "strategy_type": "index_trading",
        "parameters": {
            "confidence_threshold": 0.75,
            "risk_percentage": 1.5,
            "reward_ratio": 2.0,
            "max_signals_per_day": 6
        },
        "strategy_prompt": """
You are a US stock index specialist focused on the Dow Jones Industrial Average (DJIUSD). Your expertise is in reading institutional flows and market sentiment.

DOW JONES CHARACTERISTICS:
- Represents 30 large-cap US companies
- Sensitive to economic data and Fed policy
- Strong correlation with US dollar and bond yields
- Institutional trading creates predictable patterns
- Responds to earnings seasons and corporate news

INDEX TRADING APPROACH:
1. Economic data impact analysis (GDP, employment, inflation)
2. Federal Reserve policy and interest rate expectations
3. Corporate earnings and guidance from Dow components
4. Technical levels and institutional support/resistance
5. Market session patterns (pre-market, regular, after-hours)

SIGNAL GENERATION:
BUY signals when:
- Positive economic data supports US growth
- Fed policy is dovish or supportive
- Strong corporate earnings from Dow components
- Bounce from key technical support
- Risk-on sentiment favors equities
- Confidence ≥ 75%

SELL signals when:
- Negative economic data hurts US outlook
- Fed policy is hawkish or restrictive
- Weak corporate earnings from Dow components
- Rejection from key technical resistance
- Risk-off sentiment hurts equities
- Confidence ≥ 75%

RISK MANAGEMENT:
- Stop loss: 50-100 points for 5m, 150-250 points for 1h
- Take profit: 2:1 risk-reward minimum
- Position size: 1.5% account risk maximum
- Maximum 6 signals per day

RESPONSE FORMAT:
Always respond with JSON:
{
  "signal": true/false,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.75-1.0,
  "reasoning": "index analysis with economic and technical factors"
}

IMPORTANT: Focus on high-quality setups aligned with economic fundamentals and institutional flows.
"""
    },
    {
        "name": "EURUSD Professional Forex Trader",
        "description": "Expert-level strategy for the world's most traded currency pair EURUSD, focusing on central bank policies and economic divergence.",
        "symbols": "EURUSD",
        "timeframes": "5m,1h",
        "strategy_type": "forex_major",
        "parameters": {
            "confidence_threshold": 0.75,
            "risk_percentage": 1.5,
            "reward_ratio": 2.0,
            "max_signals_per_day": 8
        },
        "strategy_prompt": """
You are a professional forex trader specializing in EURUSD, the world's most liquid currency pair. Your expertise covers ECB and Fed policy divergence.

EURUSD FUNDAMENTALS:
- Most traded currency pair with highest liquidity
- Driven by ECB vs Fed monetary policy divergence
- Sensitive to EU and US economic data
- Responds to geopolitical events affecting EU/US
- Strong technical levels due to institutional participation

ANALYSIS FRAMEWORK:
1. Central bank policy divergence (ECB vs Fed)
2. Economic data comparison (EU vs US)
3. Interest rate differentials and expectations
4. Political stability and geopolitical events
5. Technical analysis at key levels

SIGNAL GENERATION:
BUY EUR (sell USD) when:
- ECB more hawkish than Fed expectations
- EU economic data outperforms US data
- EUR interest rates rising relative to USD
- Technical bounce from strong support
- EU political stability improves
- Confidence ≥ 75%

SELL EUR (buy USD) when:
- Fed more hawkish than ECB expectations
- US economic data outperforms EU data
- USD interest rates rising relative to EUR
- Technical rejection from strong resistance
- EU political uncertainty increases
- Confidence ≥ 75%

RISK MANAGEMENT:
- Stop loss: 15-25 pips for 5m, 40-60 pips for 1h
- Take profit: 2:1 risk-reward minimum
- Position size: 1.5% account risk maximum
- Maximum 8 signals per day

RESPONSE FORMAT:
Always respond with JSON:
{
  "signal": true/false,
  "direction": "buy" or "sell",
  "entry_price": current_price,
  "stop_loss": calculated_sl,
  "take_profit": calculated_tp,
  "confidence": 0.75-1.0,
  "reasoning": "forex analysis covering fundamentals and technicals"
}

IMPORTANT: Generate signals when fundamental and technical analysis align. EURUSD offers consistent opportunities.
"""
    }
]

async def create_default_strategies():
    """Create default prompt-based trading strategies in the database"""
    logger.info("Creating default prompt-based trading strategies")

    async for db in get_async_db():
        try:
            # Check if strategies already exist
            # Handle both synchronous and asynchronous database operations
            from src.database.setup import AsyncSessionLocal

            if AsyncSessionLocal is None:
                # Synchronous operation for SQLite
                result = db.execute(select(Strategy))
                existing_strategies = result.scalars().all()

                if existing_strategies:
                    logger.info(f"Found {len(existing_strategies)} existing strategies, skipping default creation")
                    return

                # Create default strategies
                for strategy_data in DEFAULT_STRATEGIES:
                    strategy = Strategy(
                        name=strategy_data["name"],
                        description=strategy_data["description"],
                        symbols=strategy_data["symbols"],
                        timeframes=strategy_data["timeframes"],
                        parameters=strategy_data["parameters"],
                        strategy_prompt=strategy_data["strategy_prompt"],
                        strategy_type=strategy_data.get("strategy_type", "general"),
                        code=strategy_data.get("code", ""),  # Legacy field, empty for new strategies
                        active=True
                    )

                    db.add(strategy)

                db.commit()
            else:
                # Asynchronous operation for other databases
                result = await db.execute(select(Strategy))
                existing_strategies = result.scalars().all()

                if existing_strategies:
                    logger.info(f"Found {len(existing_strategies)} existing strategies, skipping default creation")
                    return

                # Create default strategies
                for strategy_data in DEFAULT_STRATEGIES:
                    strategy = Strategy(
                        name=strategy_data["name"],
                        description=strategy_data["description"],
                        symbols=strategy_data["symbols"],
                        timeframes=strategy_data["timeframes"],
                        parameters=strategy_data["parameters"],
                        strategy_prompt=strategy_data["strategy_prompt"],
                        strategy_type=strategy_data.get("strategy_type", "general"),
                        code=strategy_data.get("code", ""),  # Legacy field, empty for new strategies
                        active=True
                    )

                    db.add(strategy)

                await db.commit()

            logger.info(f"Created {len(DEFAULT_STRATEGIES)} default strategies")

        except Exception as e:
            # Handle both synchronous and asynchronous rollback
            if AsyncSessionLocal is None:
                db.rollback()
            else:
                try:
                    await db.rollback()
                except:
                    db.rollback()

            logger.error(f"Error creating default strategies: {e}", exc_info=True)
            raise


async def add_ai_pure_strategy():
    """Add the new AI Pure Analysis Strategy if it doesn't exist"""
    logger.info("Checking for AI Pure Analysis Strategy")

    # Find the AI Pure Analysis Strategy from our defaults
    ai_strategy_data = None
    for strategy_data in DEFAULT_STRATEGIES:
        if strategy_data["name"] == "AI Pure Analysis Strategy":
            ai_strategy_data = strategy_data
            break

    if not ai_strategy_data:
        logger.error("AI Pure Analysis Strategy not found in DEFAULT_STRATEGIES")
        return

    async for db in get_async_db():
        try:
            # Check if AI Pure Analysis Strategy already exists
            from src.database.setup import AsyncSessionLocal

            if AsyncSessionLocal is None:
                # Synchronous operation for SQLite
                result = db.execute(
                    select(Strategy).where(Strategy.name == "AI Pure Analysis Strategy")
                )
                existing_strategy = result.scalars().first()

                if existing_strategy:
                    logger.info("AI Pure Analysis Strategy already exists, skipping creation")
                    return

                # Create the AI Pure Analysis Strategy
                strategy = Strategy(
                    name=ai_strategy_data["name"],
                    description=ai_strategy_data["description"],
                    symbols=ai_strategy_data["symbols"],
                    timeframes=ai_strategy_data["timeframes"],
                    parameters=ai_strategy_data["parameters"],
                    strategy_prompt=ai_strategy_data["strategy_prompt"],
                    strategy_type=ai_strategy_data.get("strategy_type", "general"),
                    code=ai_strategy_data.get("code", ""),
                    active=True
                )

                db.add(strategy)
                db.commit()
            else:
                # Asynchronous operation for other databases
                result = await db.execute(
                    select(Strategy).where(Strategy.name == "AI Pure Analysis Strategy")
                )
                existing_strategy = result.scalars().first()

                if existing_strategy:
                    logger.info("AI Pure Analysis Strategy already exists, skipping creation")
                    return

                # Create the AI Pure Analysis Strategy
                strategy = Strategy(
                    name=ai_strategy_data["name"],
                    description=ai_strategy_data["description"],
                    symbols=ai_strategy_data["symbols"],
                    timeframes=ai_strategy_data["timeframes"],
                    parameters=ai_strategy_data["parameters"],
                    strategy_prompt=ai_strategy_data["strategy_prompt"],
                    strategy_type=ai_strategy_data.get("strategy_type", "general"),
                    code=ai_strategy_data.get("code", ""),
                    active=True
                )

                db.add(strategy)
                await db.commit()

            logger.info("Successfully created AI Pure Analysis Strategy")

        except Exception as e:
            # Handle both synchronous and asynchronous rollback
            if AsyncSessionLocal is None:
                db.rollback()
            else:
                try:
                    await db.rollback()
                except:
                    db.rollback()

            logger.error(f"Error creating AI Pure Analysis Strategy: {e}", exc_info=True)
            raise
